# Cyrus Yang

**高级全栈工程师 / 技术架构师**

📧 **邮箱:** <EMAIL>
📱 **电话:** +86 18380860914
🌐 **工作地点:** 远程
💼 **LinkedIn:** [LinkedIn Profile]
🔗 **GitHub:** [GitHub Profile]
✍️ **技术博客:** [Tech Blog](https://blogxrcy.vercel.app/)

---

## 👨‍💼 个人简介

资深全栈工程师，专注于 AI 集成、高并发系统和区块链技术。拥有丰富的企业级应用开发经验，在系统架构设计、性能优化和成本控制方面有突出表现。成功交付多个高性能项目，实现显著的业务价值和技术创新。

**核心技术能力:**

- 后端开发专家 (Go 语言、微服务架构、高并发系统)
- AI 服务集成 (DeepSeek、Luma、Haiper 等多 AI 平台)
- 区块链开发 (以太坊、BSC 网络、USDT 交易监控)
- 前端开发 (React 18、Vue 3、TypeScript)
- 系统架构设计 (分布式系统、高可用、性能优化)
- 自动化平台 (n8n 工作流、API 集成、业务流程自动化)

---

## 💼 项目经历

### AI 视频生成自动化平台 | 2024.06

**技术栈:** n8n 工作流、RESTful API、JavaScript、微服务架构、异步处理

**项目描述:** 基于 n8n 开发的 AI 视频生成自动化平台，集成多个 AI 服务商，实现从文字到视频的全自动化生产流水线。

**核心贡献:**

- 集成 DeepSeek、Luma、Haiper 等多 AI 服务，实现中文文字 →AI 润色 → 视频生成的全自动化流水线
- 设计 API 热切换和容错机制，解决 API 超时等生产问题，建立降级机制
- 通过技术选型优化从付费方案切换到免费方案，**节省 100%运营成本**($29/月 → 免费)
- 系统可用性达**99%+**，支持批量视频生成和智能调度

**技术亮点:**

- 微服务架构设计，支持服务独立部署和扩展
- 异步处理机制，提升系统吞吐量和响应速度
- 智能容错和降级策略，确保服务高可用性

### 高性能 RCS 消息处理系统 & 区块链 USDT 充值系统 | 2024

**技术栈:** Go 1.21+、Gin、GORM、MySQL、Redis、以太坊客户端、Prometheus

**项目描述:** 企业级消息处理和区块链支付系统，支持高并发消息处理和实时区块链交易监控。

**核心贡献:**

- **RCS 消息系统**: 设计工作池模式，支持 10-20 个工作器并发处理，单批次处理**1000 条消息**，吞吐量**100+消息/秒**
- **多服务商集成**: 支持 Twilio、Infobip 等多个 RCS 服务商，实现智能路由和自动故障转移
- **区块链集成**: 集成 BSC 网络，实时监控 USDT 交易，Goroutine 异步处理充值确认，响应时间**<10 秒**
- **安全防护**: 实现防重复充值、金额校验、状态机管理，确保交易安全性

**技术亮点:**

- **高并发架构**: Goroutine+Channel 无锁并发，支持**1000+并发用户**
- **高性能优化**: 多层缓存架构，缓存命中率**95%+**，API 响应**<200ms**
- **分布式事务**: 保证数据一致性，支持无限层级代理体系和动态定价

### 熵界网 - 支付业务咨询平台 | 2024.12

**技术栈:** Go + Gin + GORM + SQLite + HTML/CSS/JavaScript + Viper + Logrus

**项目描述:** 企业级 Web 应用，专注支付行业垂直领域的资质代办服务平台。

**核心贡献:**

- 采用分层架构(Handler-Service-Model-Database)，实现约**5000 行高质量 Go 代码**
- 设计完整的数据模型和业务流程，包括用户注册、服务展示、需求管理、新闻资讯等核心功能
- 实现数据库迁移系统，支持版本化的数据库结构管理
- 集成多种安全措施：CSRF 防护、XSS 防护、SQL 注入防护，确保系统安全性

**技术亮点:**

- **性能优化**: 页面平均响应时间**<100ms**，支持**1000+并发用户**访问
- **工程化实践**: 模块化设计、依赖注入、配置管理、统一错误处理
- **运维支持**: 健康检查接口、完善日志记录、优雅关闭机制

### AI 智能小说编辑器 | 2024

**技术栈:** React 18 + TypeScript + Vite + DeepSeek AI

**项目描述:** 基于 React 18 开发的智能写作助手，实现类似 Cursor IDE 的 Tab 键预测功能。

**核心贡献:**

- 集成 DeepSeek-V3/R1 双模型，实现智能体裁识别和上下文分析
- 创新交互设计：灰色透明覆盖层技术 + Tab 键接受机制，用户操作流畅度高
- 性能优化：2 秒防抖机制减少**60%无效 API 调用**，本地缓存命中率**85%**
- 自研 useAutoResize Hook，实现文本区域动态适配和像素级精确定位

**技术亮点:**

- **TypeScript 严格模式**: 100%类型覆盖，编译时错误检测
- **智能缓存系统**: LRU 缓存策略，相同上下文复用预测结果
- **多环境部署**: 支持 Vercel/Netlify/Docker 多种部署方式

---

## 🛠️ 技术栈

### 编程语言

- **Go**: 专家级 (3 年+) - 高并发、微服务、区块链开发
- **JavaScript/TypeScript**: 熟练 (3 年+) - React、Vue、Node.js
- **Python**: 熟练 (2 年+) - AI 集成、数据处理、自动化脚本

### 框架与工具

- **后端**: Gin、GORM、Echo、Fiber
- **前端**: React 18、Vue 3、Vite、Webpack
- **数据库**: MySQL、PostgreSQL、SQLite、Redis
- **消息队列**: RabbitMQ、Kafka
- **监控**: Prometheus、Grafana、ELK Stack
- **容器化**: Docker、Kubernetes
- **版本控制**: Git、GitHub、GitLab

### 专业技能

- **系统架构**: 微服务架构、分布式系统、高可用设计
- **性能优化**: 缓存策略、数据库优化、并发处理
- **AI 集成**: 大语言模型 API 集成、智能化应用开发
- **区块链**: 以太坊、BSC 网络、智能合约交互
- **自动化**: 工作流设计、API 集成、业务流程自动化

---

## � Key Projects & Achievements

### Data Intelligence Platform for Business Development

**Role:** Lead Developer | **Duration:** 6 months | **Team Size:** 3 developers

**Project Overview:** Developed an automated data collection and analysis system to support business development teams in identifying and evaluating potential partnerships and market opportunities.

**Key Responsibilities:**

- Led the design and development of web scraping systems processing 10,000+ data points daily
- Implemented real-time data dashboards for trend analysis and business intelligence
- Collaborated with BD teams to understand requirements and deliver actionable insights

**Technologies Used:** Python, JavaScript, PostgreSQL, Redis
**Business Impact:** Accelerated business development processes and improved decision-making efficiency

### Multi-Platform Intelligence Aggregation System

**Role:** Full Stack Developer | **Duration:** 4 months | **Team Size:** 2 developers

**Project Overview:** Built a comprehensive monitoring and analysis system for gathering market intelligence from multiple social media platforms and official sources.

**Key Responsibilities:**

- Developed automated content collection from various platforms (Twitter, Discord, Telegram)
- Implemented sentiment analysis and filtering algorithms for relevant information identification
- Created real-time notification system for critical market signals

**Technologies Used:** Node.js, React.js, WebSocket, NLP libraries
**Business Impact:** Enabled proactive market analysis and competitive intelligence gathering

### Performance Optimization & Security Enhancement

**Role:** Senior Developer | **Duration:** Ongoing | **Team Size:** Individual contributor

**Project Overview:** Led comprehensive system optimization and security hardening initiatives across multiple applications.

**Key Responsibilities:**

- Improved system performance by 400% through algorithm optimization and caching strategies
- Implemented security measures including XSS protection and access control systems
- Developed automated security monitoring and reporting tools

**Technologies Used:** Go, Linux, Docker, Security tools
**Business Impact:** Significantly improved system reliability and security posture

---

## 🏆 Core Technical Achievements

### 🎮 Sony Hackathon Project (Media Application)

- ✅ Implemented high-concurrency media processing module using **Golang**
- ✅ Built dynamic data visualization dashboard with **React**
- ✅ Developed distributed task scheduling system using **Node.js cluster**

### 🔒 Server Security Hardening Implementation

- 🔍 Analyzed attack logs to identify SSH weak password vulnerabilities
- 🛡️ Deployed behavior-based intrusion detection system (HIDS)
- 🤖 Developed automated security inspection scripts using **Bash + Python**

### 🌐 Remote Collaboration System Architecture

- 🔄 Designed multi-stage **GitLab CI/CD** verification pipeline
- 📋 Established comprehensive code review checklist, increasing defect detection rate by **35%**
- 📊 Implemented automated test coverage reporting system

---

## 🛠️ Featured Projects & Development

### 🔧 Self-Developed Toolset (Actively Maintained)

- **CLI Scaffolding Tool**: General-purpose development accelerator built with Go Cobra
- **Chrome Extension**: API Debugging Assistant using Vue3 + TypeScript
- **VS Code Extension**: Code Snippet Security Management Tool for secure development workflows

### 🛒 E-commerce Platform Performance Optimization

- ⚡ Refactored product recommendation algorithms, achieving **400% QPS improvement**
- 🚀 Designed Redis cache penetration protection strategy
- 🔐 Implemented distributed lock service using **ETCD** for high-availability operations

### 🌐 IoT Data Platform Architecture

- 📡 Built **MQTT** message persistence pipeline for real-time data ingestion
- 🚨 Developed real-time anomaly detection module with machine learning integration
- 👥 Designed **RBAC** permission control system implemented with **JWT** authentication

### 🔐 Security Implementation & Best Practices

- 🛡️ Successfully mitigated **XSS** phishing attacks and implemented comprehensive vulnerability fixes
- 🔑 Built role-based API access control middleware for enterprise applications
- 📚 Authored internal documentation: "**Linux Server Security Configuration Guide**"

---

## 📚 Knowledge Sharing & Community Contribution

- 📝 Published **12** practical technical tutorials on personal blog
- ⭐ Open-source projects on GitHub accumulated **85+** stars from the developer community
- 🎤 Conducted **6** technical knowledge sharing sessions for team development

---

## 🚀 Web3 Transition & Future Readiness

### 🔄 Technology Migration Strategy

- **Go Language Expertise**: Directly applicable to blockchain node development and infrastructure
- **Security Background**: Seamlessly transferable to smart contract auditing and DeFi security
- **Remote Collaboration Mastery**: Perfectly aligned with DAO operational models and distributed teams

### 📖 Continuous Learning & Development

- 📚 Actively completing "**Solidity by Example**" comprehensive practical course
- 🏆 Participating in **ETHDenver 2024** hackathon and blockchain innovation challenges
- 🔗 Building and maintaining personal testnet validation node for hands-on blockchain experience

---

## 🎯 Personal Introduction

I am a passionate technologist with an insatiable curiosity for learning and exploring diverse fields beyond traditional software development. My enthusiasm for technology drives me to continuously expand my knowledge across multiple domains, including finance, trading systems, quantitative trading bots, and embedded electronics.

As a lifelong learner, I dedicate significant time to studying financial markets, reading extensively on finance theory, market psychology, and behavioral economics. This interdisciplinary approach allows me to bring unique perspectives to technical problem-solving, combining analytical thinking from financial markets with engineering principles from software development.

My interests span from building automated trading algorithms and quantitative analysis tools to experimenting with embedded systems and IoT devices. I believe that the intersection of technology, finance, and human psychology creates fascinating opportunities for innovation, particularly in the emerging Web3 and DeFi ecosystems.

Whether I'm diving deep into market microstructure, developing trading bots, or exploring the latest embedded technologies, I approach each challenge with the same methodical curiosity and passion for understanding how systems work at their core. This multidisciplinary background enables me to architect solutions that are not only technically sound but also aligned with real-world market dynamics and user behavior.
