# <PERSON> Yang

**Senior Full Stack Engineer / Technical Architect**

📧 **Email:** <EMAIL>  
📱 **Phone:** +86 18380860914  
🌐 **Location:** Remote  
💼 **LinkedIn:** [LinkedIn Profile]  
🔗 **GitHub:** [GitHub Profile]  
✍️ **Tech Blog:** [Tech Blog](https://blogxrcy.vercel.app/)

---

## 👨‍💼 Professional Summary

Full Stack Engineer with experience in Go and JavaScript/TypeScript development. Experienced in AI service integration, blockchain payment systems, and high-concurrency system development. **Achieved 100K+ revenue through technical project commercialization, with complete delivery capability from technical development to business implementation.**

**Tech Stack:**

- **Backend**: Go + Gin + GORM, microservices and concurrent programming
- **Frontend**: React 18 + TypeScript, Vue 3
- **Database**: MySQL, Redis, SQLite
- **Blockchain**: Ethereum, BSC network, self-developed USDT deposit system
- **AI Integration**: DeepSeek, Luma, Haiper API integration
- **Data Collection**: Python crawlers, Scrapy, Selenium, data cleaning
- **Tools**: Docker, Git, n8n workflows

---

## 💼 Professional Experience

### 🎬 AI Video Generation Automation Platform | 2024.06

**Tech Stack:** n8n + JavaScript + RESTful API

#### 🤔 Problem Analysis

Client needed batch video generation for marketing, facing efficiency bottlenecks:

- **Pain Point 1:** Manual processing took 15-20 minutes per video, 100 videos required 2-3 days
- **Pain Point 2:** Zapier automation cost $29/month with unstable API calls

#### 💡 Solution Strategy

**Technology Selection Analysis:**

- Compared Zapier vs n8n vs pure code solution, found n8n both free and flexible
- Analyzed AI service stability: Luma high quality but prone to timeout, Haiper fast but average quality

**Architecture Design Decision:**

- Adopted "multi-provider + intelligent degradation" strategy to solve single point of failure
- Designed pipeline: `Raw Text` → `DeepSeek Polishing` → `Luma/Haiper Video Generation`

#### 🛠️ Key Technical Implementation

**Fault Tolerance Design:**

- Auto-switch to Haiper when Luma API response exceeds 30 seconds
- Implemented exponential backoff retry: 1s → 2s → 4s, max 3 retries

**Cost Optimization Strategy:**

- Through deep research of n8n documentation, found complete replacement for Zapier paid features
- Custom JavaScript nodes handle complex logic, avoiding paid plugins

#### 📊 Solution Validation

- **Efficiency Improvement:** Single video processing time reduced from 15 minutes to 2 minutes, 87.5% improvement
- **Cost Control:** Monthly operational cost reduced from $29 to $0, 100% savings
- **Stability:** System availability improved from 85% to 99%+ through multi-provider strategy

### High-Performance RCS Message Processing System & Blockchain USDT Deposit System | 2024

**Tech Stack:** Go 1.21+, Gin, GORM, MySQL, Redis, Ethereum client, Prometheus

**Project Description:** Enterprise-level message processing and blockchain payment system supporting high-concurrency message processing and real-time blockchain transaction monitoring.

**Key Contributions:**

- **RCS Message System**: Designed worker pool pattern supporting 10-20 concurrent workers, processing **1000 messages per batch**, throughput **100+ messages/second**
- **Multi-Provider Integration**: Supported multiple RCS providers like Twilio, Infobip, implementing intelligent routing and automatic failover
- **Blockchain Integration**: **Hand-crafted USDT deposit functionality from scratch** without relying on any open-source projects, integrated BSC network for real-time USDT transaction monitoring, Goroutine asynchronous deposit confirmation processing, response time **<10 seconds**
- **Security Protection**: Implemented duplicate deposit prevention, amount validation, state machine management ensuring transaction security

**Technical Highlights:**

- **High-Concurrency Architecture**: Goroutine+Channel lock-free concurrency supporting **1000+ concurrent users**
- **High-Performance Optimization**: Multi-layer caching architecture with **95%+ cache hit rate**, API response **<200ms**
- **Distributed Transactions**: Ensuring data consistency, supporting unlimited-level agent hierarchy and dynamic pricing

### Entropy Network - Payment Business Consulting Platform | 2024.12

**Tech Stack:** Go + Gin + GORM + SQLite + HTML/CSS/JavaScript + Viper + Logrus

**Project Description:** Enterprise-level Web application focused on payment industry vertical domain qualification agency service platform.

**Key Contributions:**

- Adopted layered architecture (Handler-Service-Model-Database), implemented approximately **5000 lines of high-quality Go code**
- Designed complete data models and business processes including user registration, service display, requirement management, news and information core functions
- Implemented database migration system supporting versioned database structure management
- Integrated multiple security measures: CSRF protection, XSS protection, SQL injection protection ensuring system security

**Technical Highlights:**

- **Performance Optimization**: Average page response time **<100ms**, supporting **1000+ concurrent user** access
- **Engineering Practices**: Modular design, dependency injection, configuration management, unified error handling
- **Operations Support**: Health check interfaces, comprehensive logging, graceful shutdown mechanisms

### AI Intelligent Novel Editor | 2024

**Tech Stack:** React 18 + TypeScript + Vite + DeepSeek API

Mimics Cursor IDE's Tab key prediction functionality for novel writing assistance.

**Key Features:**

- Integrated DeepSeek-V3/R1 models
- Tab key to accept predicted text
- Intelligent genre recognition (6 literary genres)
- Context analysis (500 character truncation)

**Technical Implementation:**

- Gray transparent overlay for prediction text display
- 2-second debounce to reduce API calls
- LRU caching strategy, 85% local cache hit rate
- Custom useAutoResize Hook
- TypeScript strict mode, 100% type coverage

**Innovative Memory Mechanism:**

- **Problem:** AI models have context length limitations, causing "forgetting" of previous content in long documents
- **Solution:** Designed intelligent memory system that periodically summarizes previous content
- **Implementation:**
  - Triggers memory generation every 2000 characters
  - Uses DeepSeek to summarize key information (characters, plot, style)
  - Incorporates memory as context prefix to maintain writing coherence

### Freqtrade Quantitative Trading Platform Secondary Development | 2024

**Tech Stack:** Python + Freqtrade + Plotly + Pandas + SQLite

Secondary development based on open-source quantitative trading framework Freqtrade, enhancing backtesting mechanisms and data visualization features.

**Key Features:**

- Enhanced backtesting engine supporting multi-strategy parallel backtesting
- Developed data charting module for visualizing trading signals and profit curves
- Optimized strategy parameter tuning workflow
- Integrated risk management and capital management modules

**Technical Implementation:**

- Extended Freqtrade core backtesting logic
- Built interactive chart system using Plotly
- Pandas data processing and performance analysis
- SQLite storage for backtest results and historical data
- Custom strategy templates and indicator library

### 🕷️ Web3 Project Information Crawler System | 2024

**Tech Stack:** Python + Scrapy + BeautifulSoup + Selenium + MySQL

#### 🤔 Business Requirements & Technical Challenges

**Project Background:**

- Need for large-scale data collection and analysis of Web3 project information
- Traditional manual collection is inefficient with outdated data
- Required integration of information from multiple sources (official websites, social media, blockchain explorers)

**Technical Challenges:**

- **Anti-crawler Countermeasures:** Target websites have complex anti-crawler mechanisms
- **Data Quality:** Multi-source data formats are inconsistent, requiring intelligent cleaning and standardization
- **Real-time Requirements:** Project information changes rapidly, requiring incremental update mechanisms

#### 💡 Solution Design

**Crawler Architecture:**

- **Distributed Crawling:** Used Scrapy-Redis for distributed crawling to improve efficiency
- **Intelligent Anti-anti-crawler:** Combined proxy pools, User-Agent rotation, request frequency control
- **Multi-engine Adaptation:** Scrapy for static pages, Selenium for JavaScript-rendered pages

**Data Processing Pipeline:**

```python
# Core data processing pipeline
1. Data Crawling → Multi-source information collection
2. Data Cleaning → Deduplication, format standardization
3. Information Extraction → NLP key information extraction
4. Quality Assessment → Project credibility scoring
5. Data Storage → Structured storage to database
```

#### 🛠️ Key Technical Implementation

**Anti-crawler Countermeasures:**

- **Proxy Pool Management:** Maintained 100+ high-quality proxies with automatic detection and rotation
- **Request Disguise:** Simulated real browser behavior with randomized request intervals
- **CAPTCHA Handling:** Integrated OCR recognition for simple graphic CAPTCHAs

**Data Quality Assurance:**

- **Multi-source Verification:** Cross-verified same project information from 3+ sources
- **Anomaly Detection:** Statistical methods to identify abnormal data points
- **Incremental Updates:** Smart incremental crawling based on timestamps and content hashes

#### 📊 Project Results & Value

**Data Scale:**

- **Project Coverage:** Successfully crawled 5000+ Web3 project information
- **Data Dimensions:** 20+ dimensions including basic info, team background, funding, community activity
- **Update Frequency:** Daily updates of 500+ projects, 95%+ data freshness

**Technical Metrics:**

- **Crawling Efficiency:** Single machine processes 100K+ pages daily, 98%+ success rate
- **Data Quality:** 95%+ accuracy through multi-source verification
- **System Stability:** 7×24 stable operation, <1% failure rate

**Business Value:**

- **Investment Decision Support:** Comprehensive project information foundation for investment analysis
- **Risk Identification:** 85%+ accuracy in identifying potential risk projects through data analysis
- **Market Insights:** Discover industry trends and hotspots to assist investment strategy formulation

---

## 💼 Commercial Projects & Technical Consulting

### Independent Technical Solution Provider | 2024

#### 🎯 Business Overview

Providing customized technical solutions for multiple enterprise and individual clients, covering AI automation, blockchain payments, and web application development.

#### 💰 Commercial Results

- **Client Services:** Served 15+ clients with 100% project delivery success rate
- **Revenue Achievement:** Technical services generated **100K+ revenue**, average project profit margin 60%+
- **Client Satisfaction:** 98%, established long-term partnerships with multiple clients
- **Technical Impact:** Helped clients achieve 50-300% business efficiency improvement, 20-100% cost savings

#### 🛠️ Core Service Capabilities

**AI Automation Solutions:**

- Developed video generation automation system for e-commerce clients, saving $2000+ monthly labor costs
- Provided AI writing tools for content creators, improving creation efficiency by 5x

**Blockchain Payment Systems:**

- Developed USDT deposit system for fintech companies, saving 1% third-party fees
- Integrated cryptocurrency payments for gaming companies, expanding overseas market revenue by 30%

**Enterprise Web Applications:**

- Developed business management platform for consulting companies, improving client management efficiency by 200%
- Provided digital transformation solutions for traditional enterprises, optimizing business processes by 40%

#### 🎯 Business Mindset Demonstration

- **Requirement Insight:** Deep understanding of client business pain points, providing targeted technical solutions
- **Cost Control:** Through technology selection optimization, helping clients achieve cost savings and efficiency improvements
- **Value Delivery:** Not just delivering code, but delivering quantifiable business value
- **Long-term Cooperation:** Establishing technical advisory relationships, providing continuous technical support for clients

---

## 🛠️ Technical Stack

**Programming Languages:** Go, JavaScript/TypeScript, Python
**Backend Frameworks:** Gin, GORM
**Frontend Frameworks:** React 18, Vue 3, TypeScript
**Databases:** MySQL, Redis, SQLite
**Blockchain:** Ethereum, BSC network
**Tools:** Docker, Git, n8n, Vite, Plotly

---

## 🎓 Education

**Bachelor's Degree in Computer Science**
Relevant Coursework: Data Structures, Algorithms, Database Systems, Network Security
