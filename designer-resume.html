<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>杨警名 - 工业设计师</title>
    <style>
      /* 真正的工业设计美学 - <PERSON><PERSON> + 瑞士设计 */
      @import url("https://fonts.googleapis.com/css2?family=Helvetica+Neue:wght@300;400;500;700&display=swap");

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        --black: #000000;
        --dark-gray: #333333;
        --medium-gray: #666666;
        --light-gray: #999999;
        --very-light-gray: #f5f5f5;
        --white: #ffffff;
        --grid-unit: 24px;
      }

      body {
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        background: var(--white);
        color: var(--black);
        line-height: 1.4;
        font-weight: 400;
        font-size: 16px;
        -webkit-font-smoothing: antialiased;
        margin: 0;
        padding: 0;
      }

      .resume-container {
        max-width: 210mm; /* A4 width */
        min-height: 297mm; /* A4 height */
        margin: 0 auto;
        background: var(--white);
        display: grid;
        grid-template-columns: 1fr 2fr;
        grid-gap: 0;
        position: relative;
      }

      /* 左侧边栏 */
      .sidebar {
        background: var(--primary-black);
        color: var(--warm-white);
        padding: 60px 40px;
        position: relative;
      }

      .sidebar::before {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 1px;
        height: 100%;
        background: linear-gradient(
          180deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
      }

      .profile-section {
        text-align: center;
        margin-bottom: 50px;
      }

      .profile-image {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--accent-color), #8b5cf6);
        margin: 0 auto 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 48px;
        font-weight: 300;
        color: white;
      }

      .name {
        font-family: "Space Grotesk", sans-serif;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 8px;
        letter-spacing: -0.02em;
      }

      .title {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 300;
        margin-bottom: 40px;
      }

      .contact-info {
        margin-bottom: 50px;
      }

      .contact-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
      }

      .contact-item .icon {
        width: 20px;
        margin-right: 12px;
        opacity: 0.7;
      }

      .sidebar-section {
        margin-bottom: 40px;
      }

      .sidebar-title {
        font-family: "Space Grotesk", sans-serif;
        font-size: 14px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        margin-bottom: 20px;
        color: rgba(255, 255, 255, 0.6);
      }

      .skill-item {
        margin-bottom: 16px;
      }

      .skill-name {
        font-size: 14px;
        margin-bottom: 6px;
        color: rgba(255, 255, 255, 0.9);
      }

      .skill-bar {
        height: 2px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 1px;
        overflow: hidden;
      }

      .skill-progress {
        height: 100%;
        background: linear-gradient(90deg, var(--accent-color), #8b5cf6);
        border-radius: 1px;
        transition: width 1s ease-out;
      }

      /* 主要内容区域 */
      .main-content {
        padding: 60px 50px;
        background: var(--warm-white);
      }

      .section {
        margin-bottom: 60px;
      }

      .section-title {
        font-family: "Space Grotesk", sans-serif;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 30px;
        color: var(--primary-black);
        position: relative;
        padding-bottom: 12px;
      }

      .section-title::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 40px;
        height: 2px;
        background: var(--accent-color);
      }

      .intro-text {
        font-size: 18px;
        line-height: 1.7;
        color: var(--secondary-gray);
        margin-bottom: 30px;
        font-weight: 300;
      }

      .highlight {
        color: var(--primary-black);
        font-weight: 500;
      }

      /* 教育背景 */
      .education-item {
        padding: 25px 0;
        border-bottom: 1px solid var(--border-color);
      }

      .education-item:last-child {
        border-bottom: none;
      }

      .education-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;
      }

      .education-school {
        font-size: 18px;
        font-weight: 600;
        color: var(--primary-black);
      }

      .education-year {
        font-size: 14px;
        color: var(--text-muted);
        font-weight: 500;
      }

      .education-degree {
        font-size: 16px;
        color: var(--secondary-gray);
        margin-bottom: 8px;
      }

      /* 项目经历 */
      .project-item {
        margin-bottom: 40px;
        padding: 30px;
        background: var(--light-gray);
        border-radius: 8px;
        border-left: 4px solid var(--accent-color);
        transition: all 0.3s ease;
      }

      .project-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-subtle);
      }

      .project-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
      }

      .project-title {
        font-family: "Space Grotesk", sans-serif;
        font-size: 20px;
        font-weight: 600;
        color: var(--primary-black);
        margin-bottom: 5px;
      }

      .project-role {
        font-size: 14px;
        color: var(--accent-color);
        font-weight: 500;
      }

      .project-description {
        font-size: 16px;
        line-height: 1.7;
        color: var(--secondary-gray);
        margin-bottom: 20px;
      }

      .project-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      .tag {
        padding: 6px 12px;
        background: var(--warm-white);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        font-size: 12px;
        color: var(--secondary-gray);
        font-weight: 500;
      }

      /* 作品集链接 */
      .portfolio-section {
        text-align: center;
        padding: 40px;
        background: var(--light-gray);
        border-radius: 8px;
      }

      .portfolio-link {
        display: inline-flex;
        align-items: center;
        gap: 12px;
        padding: 16px 32px;
        background: var(--primary-black);
        color: var(--warm-white);
        text-decoration: none;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
        font-size: 16px;
      }

      .portfolio-link:hover {
        background: var(--accent-color);
        transform: translateY(-1px);
      }

      .portfolio-description {
        margin-top: 16px;
        font-size: 14px;
        color: var(--text-muted);
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        body {
          padding: 20px 10px;
        }

        .resume-container {
          grid-template-columns: 1fr;
          max-width: 100%;
        }

        .sidebar {
          padding: 40px 30px;
        }

        .main-content {
          padding: 40px 30px;
        }

        .section-title {
          font-size: 20px;
        }

        .project-item {
          padding: 20px;
        }
      }

      @media (max-width: 480px) {
        .sidebar {
          padding: 30px 20px;
        }

        .main-content {
          padding: 30px 20px;
        }

        .name {
          font-size: 24px;
        }

        .section-title {
          font-size: 18px;
        }
      }
    </style>
  </head>
  <body>
    <div class="resume-container">
      <!-- 左侧边栏 -->
      <aside class="sidebar">
        <div class="profile-section">
          <div class="profile-image">杨</div>
          <h1 class="name">杨警名</h1>
          <p class="title">工业设计师</p>
        </div>

        <div class="contact-info">
          <div class="contact-item">
            <span class="icon">📧</span>
            <span><EMAIL></span>
          </div>
          <div class="contact-item">
            <span class="icon">📱</span>
            <span>138-0000-0000</span>
          </div>
          <div class="contact-item">
            <span class="icon">📍</span>
            <span>成都，四川</span>
          </div>
          <div class="contact-item">
            <span class="icon">🌐</span>
            <span>risingai.xyz/xrcyblog</span>
          </div>
        </div>

        <div class="sidebar-section">
          <h3 class="sidebar-title">核心技能</h3>
          <div class="skill-item">
            <div class="skill-name">Unity3D & 游戏设计</div>
            <div class="skill-bar">
              <div class="skill-progress" style="width: 90%"></div>
            </div>
          </div>
          <div class="skill-item">
            <div class="skill-name">SolidWorks & 3D建模</div>
            <div class="skill-bar">
              <div class="skill-progress" style="width: 85%"></div>
            </div>
          </div>
          <div class="skill-item">
            <div class="skill-name">AR/VR 交互设计</div>
            <div class="skill-bar">
              <div class="skill-progress" style="width: 80%"></div>
            </div>
          </div>
          <div class="skill-item">
            <div class="skill-name">C# 编程</div>
            <div class="skill-bar">
              <div class="skill-progress" style="width: 75%"></div>
            </div>
          </div>
          <div class="skill-item">
            <div class="skill-name">KeyShot 渲染</div>
            <div class="skill-bar">
              <div class="skill-progress" style="width: 85%"></div>
            </div>
          </div>
        </div>

        <div class="sidebar-section">
          <h3 class="sidebar-title">软件工具</h3>
          <div
            style="
              color: rgba(255, 255, 255, 0.8);
              font-size: 14px;
              line-height: 1.6;
            "
          >
            SolidWorks • Rhino • KeyShot<br />
            V-Ray • Fusion 360 • Unity3D<br />
            Arduino • ESP32 • C#<br />
            Photoshop • Illustrator
          </div>
        </div>
      </aside>

      <!-- 主要内容区域 -->
      <main class="main-content">
        <!-- 个人简介 -->
        <section class="section">
          <h2 class="section-title">关于我</h2>
          <p class="intro-text">
            我是一名<span class="highlight">工业设计师</span
            >，专注于游戏设计、AR/VR交互体验和智能产品设计。 具备<span
              class="highlight"
              >Unity3D游戏开发</span
            >和<span class="highlight">硬件交互编程</span>的跨界能力，
            能够将创意设计与技术实现完美结合，创造出既美观又实用的产品体验。
          </p>
        </section>

        <!-- 教育背景 -->
        <section class="section">
          <h2 class="section-title">教育背景</h2>
          <div class="education-item">
            <div class="education-header">
              <div>
                <div class="education-school">西南科技大学</div>
                <div class="education-degree">工业设计专业 · 本科</div>
              </div>
              <div class="education-year">2022 - 2026</div>
            </div>
          </div>
        </section>

        <!-- 项目经历 -->
        <section class="section">
          <h2 class="section-title">项目经历</h2>

          <div class="project-item">
            <div class="project-header">
              <div>
                <h3 class="project-title">2D游戏《王国之梦》设计与开发</h3>
                <p class="project-role">主设计师 & 核心开发者</p>
              </div>
            </div>
            <p class="project-description">
              利用Unity引擎开发完成一款完整的2D游戏。负责游戏世界观构建、关卡设计、角色与场景的美术风格定义，并进行UI/UX设计，确保游戏的易玩性和沉浸感。在Unity中进行场景搭建、角色动画整合、音效集成，实现核心游戏逻辑和交互系统。
            </p>
            <div class="project-tags">
              <span class="tag">Unity3D</span>
              <span class="tag">游戏设计</span>
              <span class="tag">UI/UX设计</span>
              <span class="tag">C#编程</span>
              <span class="tag">关卡设计</span>
            </div>
          </div>

          <div class="project-item">
            <div class="project-header">
              <div>
                <h3 class="project-title">AR交互体验原型设计与开发</h3>
                <p class="project-role">核心设计师 & Unity开发者</p>
              </div>
            </div>
            <p class="project-description">
              基于Unity平台开发AR应用原型。负责定义AR场景中虚拟物体与真实环境的交互逻辑，设计直观的用户界面和用户体验，确保虚实融合的自然感。利用Unity引擎进行3D模型导入、材质渲染，实现空间识别与追踪功能。
            </p>
            <div class="project-tags">
              <span class="tag">AR开发</span>
              <span class="tag">Unity3D</span>
              <span class="tag">空间交互</span>
              <span class="tag">3D建模</span>
              <span class="tag">用户体验</span>
            </div>
          </div>

          <div class="project-item">
            <div class="project-header">
              <div>
                <h3 class="project-title">AI视频生成平台界面设计</h3>
                <p class="project-role">UI/UX设计师</p>
              </div>
            </div>
            <p class="project-description">
              为AI视频生成自动化平台设计用户界面，集成多个AI服务。负责整体用户体验设计，实现中文文字→AI润色→视频生成的全自动化流水线界面，注重操作流程的直观性和易用性。
            </p>
            <div class="project-tags">
              <span class="tag">界面设计</span>
              <span class="tag">用户体验</span>
              <span class="tag">流程设计</span>
              <span class="tag">AI产品</span>
            </div>
          </div>
        </section>

        <!-- 作品集 -->
        <section class="section">
          <div class="portfolio-section">
            <h2 class="section-title">在线作品集</h2>
            <a
              href="https://risingai.xyz/xrcyblog"
              target="_blank"
              class="portfolio-link"
            >
              <span>🎨</span>
              <span>查看我的设计作品</span>
              <span>→</span>
            </a>
            <p class="portfolio-description">
              包含完整的设计项目案例、设计思路分析和技术实现过程
            </p>
          </div>
        </section>
      </main>
    </div>
  </body>
</html>
