<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>杨 (<PERSON>) - 简历</title>
    <!-- Link to Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Custom styles to ensure Inter font is used and enhance aesthetics -->
    <style>
      /* Base styles for a clean, modern look, similar to Apple's aesthetic */
      body {
        font-family: "Inter", sans-serif; /* Keep Inter, which is clean and modern */
        background-color: #f8f8fa; /* A very subtle, slightly cooler light gray background */
        display: flex;
        justify-content: center;
        align-items: flex-start;
        min-height: 100vh;
        padding: 4rem 2rem; /* Increased padding around the resume container */
      }
      .resume-container {
        max-width: 840px; /* Slightly wider for more content breathing room */
        width: 100%;
        background-color: #ffffff;
        border-radius: 1rem; /* Consistent, crisp border radius */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03); /* Even more subtle shadow */
        padding: 4rem 3.5rem; /* Increased internal padding for generous whitespace */
        box-sizing: border-box;
        line-height: 1.8; /* Generous line height for improved readability */
      }

      /* Typography and Headings */
      h1,
      h2,
      h3,
      h4 {
        color: #1c1c1e; /* A very deep, almost black gray for strong headings */
        letter-spacing: -0.015em; /* Subtle tighter letter spacing for headings */
      }
      h1 {
        font-size: 3.5rem; /* Larger, more impactful name */
        font-weight: 700; /* Bold for the name */
        margin-bottom: 0.8rem;
      }
      h2 {
        font-size: 2.125rem; /* Clear section headings */
        font-weight: 500; /* Medium weight for a softer feel */
        border-bottom: 1px solid #ededed; /* Extremely light, almost invisible separator */
        padding-bottom: 1rem;
        margin-top: 3.5rem; /* More space above sections */
        margin-bottom: 2rem;
      }
      h3 {
        font-size: 1.625rem; /* Project titles */
        font-weight: 600; /* Semibold for project titles */
        color: #2c2c2e; /* Slightly lighter than h1/h2 */
        margin-bottom: 0.8rem;
      }
      h4 {
        font-size: 1.375rem; /* Sub-headings within projects */
        font-weight: 600; /* Semibold for sub-sections */
        color: #3a3a3c; /* A softer dark gray */
        margin-top: 2rem; /* More space above sub-section headings */
        margin-bottom: 0.8rem;
      }
      p,
      li {
        color: #3c3c3c; /* Softer dark gray for body text, high contrast but not harsh black */
        line-height: 1.8; /* Increased line height for airy feel */
      }
      ul {
        list-style-type: disc; /* Keep bullet points for lists */
        margin-left: 1.5rem; /* Slightly increased indent */
      }
      ul.no-bullets {
        list-style-type: none;
        margin-left: 0;
      }

      /* Links and Interactive Elements */
      a {
        color: #007aff; /* Apple's vibrant blue */
        text-decoration: none; /* No underline by default */
        transition: color 0.2s ease-in-out, text-decoration 0.2s ease-in-out;
        font-weight: 500; /* Medium weight for links */
      }
      a:hover {
        color: #006adc; /* Slightly darker blue on hover */
        text-decoration: underline; /* Underline on hover for clarity */
      }

      /* Tags for skills */
      .tag {
        display: inline-block;
        background-color: #e8e8e8; /* Very light gray background for tags */
        color: #666666; /* Medium gray text */
        padding: 0.45rem 1rem; /* Slightly larger padding */
        border-radius: 1.25rem; /* More rounded, pill-like shape */
        font-size: 0.95rem; /* Slightly larger font for tags */
        margin-right: 0.75rem; /* Increased spacing between tags */
        margin-bottom: 0.75rem;
        font-weight: 500; /* Medium weight for tags */
        transition: background-color 0.2s ease;
      }
      .tag:hover {
        background-color: #dcdcdc; /* Subtle hover effect for tags */
      }

      /* Icons - using minimalist approach, actual SVG/FontAwesome preferred */
      .icon {
        display: inline-block;
        width: 1.35rem; /* Slightly larger icon size */
        height: 1.35rem;
        margin-right: 0.6rem;
        vertical-align: middle; /* Better vertical alignment */
        color: #888888; /* Subtle icon color */
      }
      /* Hide placeholder text for icons, if actual icons are not used */
      .icon-placeholder {
        display: none; /* Hide placeholder text in final design */
      }
      .code-block {
        background-color: #fafafa; /* Slightly lighter background for code block */
        border-left: 3px solid #007aff; /* Match Apple blue */
        padding: 1.2rem 1.5rem; /* Increased padding */
        margin-top: 1.5rem;
        margin-bottom: 1.5rem;
        font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo,
          Courier, monospace;
        font-size: 0.9em;
        color: #333;
        overflow-x: auto;
        border-radius: 0.35rem; /* Slightly more rounded corners */
      }
      .text-strong-emphasis {
        font-weight: 600; /* Semibold for bold text within paragraphs/lists */
        color: #1a1a1a;
      }
    </style>
  </head>
  <body>
    <div class="resume-container">
      <!-- Header Section -->
      <header class="text-center mb-12 pb-10 border-b border-gray-100">
        <h1 class="text-6xl font-extrabold text-gray-900 leading-tight">
          杨 (Cyrus Yang)
        </h1>
        <p class="text-3xl font-medium text-gray-700 mt-3">
          高级全栈工程师 / 技术架构师
        </p>
        <div
          class="flex flex-wrap justify-center items-center gap-x-10 gap-y-4 mt-10 text-lg text-gray-600"
        >
          <span class="flex items-center">
            <svg
              class="icon"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"
              ></path>
              <path
                d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"
              ></path>
            </svg>
            <EMAIL>
          </span>
          <span class="flex items-center">
            <svg
              class="icon"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.774a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
              ></path>
            </svg>
            +86 18380860914
          </span>
          <span class="flex items-center">
            <svg
              class="icon"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                clip-rule="evenodd"
              ></path>
            </svg>
            工作地点: 远程
          </span>
        </div>
        <div class="flex justify-center items-center gap-10 mt-6 text-lg">
          <a href="#" target="_blank">
            <img
              src="https://placehold.co/24x24/FFFFFF/000000?text=in"
              alt="LinkedIn icon"
              class="icon hidden"
            />
            <span class="icon-placeholder">in</span> LinkedIn Profile
          </a>
          <a href="#" target="_blank">
            <img
              src="https://placehold.co/24x24/FFFFFF/000000?text=GH"
              alt="GitHub icon"
              class="icon hidden"
            />
            <span class="icon-placeholder">GH</span> GitHub Profile
          </a>
          <a href="https://blogxrcy.vercel.app/" target="_blank">
            <img
              src="https://placehold.co/24x24/FFFFFF/000000?text=Blog"
              alt="Blog icon"
              class="icon hidden"
            />
            <span class="icon-placeholder">https://blogxrcy.vercel.app/</span>
            https://blogxrcy.vercel.app/
          </a>
        </div>
      </header>

      <!-- Personal Summary Section -->
      <section class="mb-12">
        <h2 class="section-heading">个人简介</h2>
        <p class="mb-6 text-gray-700">
          全栈工程师，主要使用 Go 和 JavaScript/TypeScript 开发。拥有 AI
          服务集成、区块链支付系统和高并发系统开发经验。<span
            class="text-strong-emphasis"
            >通过技术项目商业化实现营收 10
            万+，具备从技术开发到商业落地的完整交付能力。</span
          >
        </p>
        <h3 class="text-2xl font-semibold mb-5 text-gray-800">核心技术栈:</h3>
        <div class="flex flex-wrap gap-2">
          <span class="tag">Go</span>
          <span class="tag">Gin</span>
          <span class="tag">GORM</span>
          <span class="tag">微服务</span>
          <span class="tag">并发编程</span>
          <span class="tag">React 18</span>
          <span class="tag">TypeScript</span>
          <span class="tag">Vue 3</span>
          <span class="tag">MySQL</span>
          <span class="tag">Redis</span>
          <span class="tag">SQLite</span>
          <span class="tag">以太坊</span>
          <span class="tag">BSC 网络</span>
          <span class="tag">DeepSeek API</span>
          <span class="tag">Luma API</span>
          <span class="tag">Haiper API</span>
          <span class="tag">Docker</span>
          <span class="tag">Git</span>
          <span class="tag">n8n 工作流</span>
          <span class="tag">Python 爬虫</span>
          <span class="tag">Scrapy</span>
          <span class="tag">Selenium</span>
          <span class="tag">数据清洗</span>
          <span class="tag">Vite</span>
        </div>
      </section>

      <!-- Project Experience Section -->
      <section class="mb-12">
        <h2 class="section-heading">项目经历</h2>

        <!-- Project 1: AI 视频生成自动化平台 -->
        <div class="mb-10 pb-8 border-b border-gray-100 last:border-b-0">
          <h3 class="text-2xl font-bold text-gray-800 mb-3">
            AI 视频生成自动化平台
            <span class="text-base font-normal text-gray-500 ml-2"
              >| 2024.06</span
            >
          </h3>
          <p class="text-gray-600 mb-4">
            <strong>技术栈:</strong> n8n + JavaScript + RESTful API
          </p>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">问题分析</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">痛点 1:</span>
              手动操作每个视频需 15-20 分钟，批量处理 100 个视频需要 2-3 天。
            </li>
            <li>
              <span class="text-strong-emphasis">痛点 2:</span> 使用 Zapier
              自动化方案成本高($29/月)，且 API 调用不稳定。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">解决思路</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">技术选型分析:</span> 对比了
              Zapier vs n8n vs 纯代码方案，最终选择了免费且灵活的 n8n。
            </li>
            <li>
              <span class="text-strong-emphasis">AI 服务商稳定性分析:</span>
              Luma 质量高但易超时，Haiper 速度快但效果一般。
            </li>
            <li>
              <span class="text-strong-emphasis">架构设计决策:</span>
              采用"多服务商 + 智能降级"策略解决单点故障，设计流水线：<code
                >原始文字</code
              >
              → <code>DeepSeek 润色</code> → <code>Luma/Haiper 生成视频</code>。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">关键技术实现</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">容错机制设计:</span> 当 Luma
              API 响应超过 30 秒时，自动切换到 Haiper；实现指数退避重试：1s → 2s
              → 4s，最多重试 3 次。
            </li>
            <li>
              <span class="text-strong-emphasis">成本优化策略:</span> 深入研究
              n8n 文档，发现可完全替代 Zapier 付费功能；自定义 JavaScript
              节点处理复杂逻辑，避免使用付费插件。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">解决效果验证</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">效率提升:</span>
              单视频处理时间从 15 分钟降至 2 分钟，提升 87.5%。
            </li>
            <li>
              <span class="text-strong-emphasis">成本控制:</span> 月运营成本从
              $29 降至 $0，节省 100%。
            </li>
            <li>
              <span class="text-strong-emphasis">稳定性:</span>
              通过多服务商策略，系统可用性从 85% 提升至 99%+。
            </li>
          </ul>
        </div>

        <!-- Project 2: USDT 充值系统 & RCS 消息处理 -->
        <div class="mb-10 pb-8 border-b border-gray-100 last:border-b-0">
          <h3 class="text-2xl font-bold text-gray-800 mb-3">
            USDT 充值系统 & RCS 消息处理
            <span class="text-base font-normal text-gray-500 ml-2">| 2024</span>
          </h3>
          <p class="text-gray-600 mb-4">
            <strong>技术栈:</strong> Go + Gin + GORM + MySQL + Redis +
            以太坊客户端
          </p>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            核心挑战：USDT 充值系统从零设计
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">业务痛点分析:</span>
              现有开源方案功能不全或安全性存疑；第三方服务费用高昂；需要支持高频交易监控，响应时间要求
              &lt;10 秒。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">技术决策思路</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">为什么选择完全自研？</span>
              安全性考虑；成本分析 (一次性投入 vs 长期支付)；技术挑战 (验证 Go
              语言在区块链开发中的能力)。
            </li>
            <li>
              <span class="text-strong-emphasis">架构设计推理:</span> 选择
              Goroutine + Channel 无锁设计实现并发；采用 Redis + MySQL
              双写策略保证数据一致性；通过 WebSocket 监听 BSC 节点事件进行监控。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">关键技术突破</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">区块链集成难点解决:</span>
              <div class="code-block">
                <pre><code>// 核心思路：事件监听 + 异步确认
1. 监听 BSC 网络 USDT 合约事件
2. 解析 Transfer 事件，提取转账信息
3. 异步验证交易确认数(12个确认)
4. 状态机管理：pending → confirming → confirmed</code></pre>
              </div>
            </li>
            <li>
              <span class="text-strong-emphasis">防重复充值设计:</span> 基于
              txHash + blockNumber 生成唯一键，Redis 分布式锁防重。
            </li>
            <li>
              <span class="text-strong-emphasis">性能优化思路:</span> 针对
              JSON-RPC 调用瓶颈，采用连接池 + 批量查询优化。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            RCS 消息系统协同设计
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">工作池模式:</span> 借鉴 USDT
              系统的并发设计，实现 10-20 个 Goroutine 并发处理。
            </li>
            <li>
              <span class="text-strong-emphasis">多服务商容错:</span>
              应用相同的降级策略，支持 Twilio、Infobip 自动切换。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">最终效果验证</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">性能指标:</span> USDT
              充值响应时间 &lt;10 秒，RCS 消息吞吐量 100+ 条/秒。
            </li>
            <li>
              <span class="text-strong-emphasis">稳定性:</span> 运行 6
              个月零故障，处理 10 万+ 笔 USDT 交易。
            </li>
            <li>
              <span class="text-strong-emphasis">成本节省:</span>
              相比第三方方案节省 1% 手续费，月节省 $2000+。
            </li>
          </ul>
        </div>

        <!-- Project 3: 熵界网 - 支付牌照咨询平台 -->
        <div class="mb-10 pb-8 border-b border-gray-100 last:border-b-0">
          <h3 class="text-2xl font-bold text-gray-800 mb-3">
            熵界网 - 支付牌照咨询平台
            <span class="text-base font-normal text-gray-500 ml-2"
              >| 2024.12</span
            >
          </h3>
          <p class="text-gray-600 mb-4">
            <strong>技术栈:</strong> Go + Gin + GORM + SQLite + HTML/CSS/JS
          </p>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            项目背景与技术选型思考
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">业务需求分析:</span>
              客户需要快速上线(2 周内)，成本控制，后期易维护的支付行业咨询平台。
            </li>
            <li>
              <span class="text-strong-emphasis">技术选型推理过程:</span>
              数据库选择 SQLite (数据量小，部署简单)；框架选择 Gin
              (生态丰富，中间件支持好)；架构模式采用经典分层架构。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">架构设计决策</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">分层架构设计思路:</span>
              <div class="code-block">
                <pre><code>Handler Layer    (路由和请求处理)
   ↓
Service Layer    (业务逻辑封装)
   ↓
Model Layer      (数据模型定义)
   ↓
Database Layer   (数据持久化)</code></pre>
              </div>
            </li>
            <li>
              <span class="text-strong-emphasis">关键设计考虑:</span>
              通过构造函数注入实现依赖注入；使用 Viper
              支持多环境配置；统一错误码设计。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            工程化实践思路
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">数据库迁移策略:</span>
              设计版本化迁移系统，保证多环境数据库结构一致性。
            </li>
            <li>
              <span class="text-strong-emphasis">安全防护设计:</span> 识别
              CSRF、XSS、SQL 注入威胁，并采用 Token 验证、模板自动转义 + CSP
              头、GORM 参数化查询等策略进行防护。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">性能优化思路</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">瓶颈预判:</span>
              预期瓶颈在数据库查询和静态资源加载。
            </li>
            <li>
              <span class="text-strong-emphasis">优化策略:</span>
              数据库索引优化；静态资源 CDN 加速；分页查询。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">项目成果</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">开发效率:</span> 2
              周内完成开发部署，比预期提前 3 天。
            </li>
            <li>
              <span class="text-strong-emphasis">代码质量:</span> 5000
              行代码，模块化程度高，后期维护成本低。
            </li>
            <li>
              <span class="text-strong-emphasis">性能表现:</span> 页面响应时间
              &lt;100ms，支持 1000+ 并发访问。
            </li>
          </ul>
        </div>

        <!-- Project 4: AI 智能小说编辑器 -->
        <div class="mb-10 pb-8 border-b border-gray-100 last:border-b-0">
          <h3 class="text-2xl font-bold text-gray-800 mb-3">
            AI 智能小说编辑器
            <span class="text-base font-normal text-gray-500 ml-2">| 2024</span>
          </h3>
          <p class="text-gray-600 mb-4">
            <strong>技术栈:</strong> React 18 + TypeScript + Vite + DeepSeek API
          </p>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            创新灵感与技术挑战
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">产品灵感来源:</span> 受 Cursor
              IDE Tab 键预测功能启发，结合 AI 写作工具的市场痛点。
            </li>
            <li>
              <span class="text-strong-emphasis">核心技术挑战:</span>
              精确文本定位、平衡预测准确性与 API 成本、优化用户体验。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            关键技术突破思路
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">文本定位算法设计:</span>
              通过创建隐藏 span 元素，利用浏览器原生渲染精确计算预测文本位置。
            </li>
            <li>
              <span class="text-strong-emphasis">AI 调用优化策略:</span> 2
              秒防抖、上下文截取 (500 字符)、LRU 缓存 (命中率 85%) 降低 API
              调用成本。
            </li>
            <li>
              <span class="text-strong-emphasis">Memory 机制创新设计:</span>
              设计智能记忆系统，每 2000 字符触发记忆生成，利用 DeepSeek
              总结前文关键信息，保持写作连贯性。
            </li>
            <li>
              <span class="text-strong-emphasis">交互体验设计:</span>
              灰色透明覆盖层 + Tab 键接受机制，以及自研 useAutoResize Hook
              实现文本区域动态适配。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">工程化实践</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">TypeScript 严格模式应用:</span>
              100% 类型覆盖，减少运行时类型错误，提升开发效率。
            </li>
            <li>
              <span class="text-strong-emphasis">模块化架构设计:</span>
              <div class="code-block">
                <pre><code>components/     (UI 组件层)
hooks/         (业务逻辑层)
services/      (API 调用层)
utils/         (工具函数层)</code></pre>
              </div>
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            效果验证与优化
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">用户体验:</span> Tab 键接受率
              90%+，用户反馈操作流畅。
            </li>
            <li>
              <span class="text-strong-emphasis">性能指标:</span> 本地缓存命中率
              85%，API 调用成本降低 60%。
            </li>
            <li>
              <span class="text-strong-emphasis">Memory 效果:</span>
              长文档（5000+ 字）写作连贯性提升 70%，解决了 AI "遗忘"问题。
            </li>
            <li>
              <span class="text-strong-emphasis">技术价值:</span> 验证了 AI +
              前端的创新交互模式可行性，Memory 机制为行业首创。
            </li>
          </ul>
        </div>

        <!-- Project 5: Freqtrade 量化交易平台二次开发 -->
        <div class="mb-10 pb-8 border-b border-gray-100 last:border-b-0">
          <h3 class="text-2xl font-bold text-gray-800 mb-3">
            Freqtrade 量化交易平台二次开发
            <span class="text-base font-normal text-gray-500 ml-2">| 2024</span>
          </h3>
          <p class="text-gray-600 mb-4">
            <strong>技术栈:</strong> Python + Freqtrade + Plotly + Pandas +
            SQLite
          </p>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            为什么选择二次开发而非从零开始？
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">技术调研过程:</span> 对比了
              Zipline、Backtrader、Freqtrade，选择 Freqtrade
              因其社区活跃、模块化设计好、易于扩展。
            </li>
            <li>
              <span class="text-strong-emphasis">痛点分析:</span> 原版回测局限性
              (单策略)；可视化缺失；手动调参效率低。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            二次开发策略思路
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">架构扩展设计:</span> 保持与原有
              API 兼容，通过插件机制扩展功能；新增
              <code>backtest_analyzer</code> 和
              <code>visualization</code> 模块；设计统一数据接口。
            </li>
            <li>
              <span class="text-strong-emphasis">并行回测实现思路:</span>
              <div class="code-block">
                <pre><code># 核心设计思路：进程池 + 结果聚合
1. 策略配置解析 → 多个独立配置
2. 进程池并行执行 → 充分利用多核CPU
3. 结果收集聚合 → 统一格式输出
4. 性能对比分析 → 生成排名报告</code></pre>
              </div>
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">关键技术实现</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">数据可视化模块设计:</span> 选择
              Plotly 实现交互式图表 (收益曲线图、回撤分析图、交易信号图)。
            </li>
            <li>
              <span class="text-strong-emphasis">参数优化算法:</span>
              实现贝叶斯优化算法，减少 70% 计算时间，自动寻找最优参数组合。
            </li>
            <li>
              <span class="text-strong-emphasis">风险管理模块:</span>
              实现动态仓位管理、止损止盈、相关性分析功能。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            开发成果与验证
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">功能完整性:</span> 新增 5
              个核心模块，代码量增加 3000+ 行。
            </li>
            <li>
              <span class="text-strong-emphasis">性能提升:</span>
              并行回测速度提升 4 倍，支持同时测试 10+ 策略。
            </li>
            <li>
              <span class="text-strong-emphasis">实用价值:</span>
              帮助识别出年化收益 15%+ 且最大回撤 &lt;8% 的稳定策略。
            </li>
            <li>
              <span class="text-strong-emphasis">开源贡献:</span> 部分功能已提交
              PR 到 Freqtrade 主仓库。
            </li>
          </ul>
        </div>

        <!-- Project 6: Web3 项目信息爬虫系统 -->
        <div class="mb-10 pb-8">
          <h3 class="text-2xl font-bold text-gray-800 mb-3">
            Web3 项目信息爬虫系统
            <span class="text-base font-normal text-gray-500 ml-2">| 2024</span>
          </h3>
          <p class="text-gray-600 mb-4">
            <strong>技术栈:</strong> Python + Scrapy + BeautifulSoup + Selenium
            + MySQL
          </p>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            业务需求与技术挑战
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">项目背景:</span> 需要对 Web3
              项目方信息进行大规模数据收集和分析，解决传统手动收集效率低和数据不及时问题。
            </li>
            <li>
              <span class="text-strong-emphasis">技术挑战:</span>
              反爬虫对抗、多源数据质量统一与清洗、实时增量更新机制。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">解决方案设计</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">爬虫架构设计:</span> 使用
              Scrapy-Redis 实现分布式爬虫；结合代理池、User-Agent
              轮换、请求频率控制实现智能反反爬；Scrapy 处理静态页面，Selenium
              处理 JavaScript 渲染页面。
            </li>
            <li>
              <span class="text-strong-emphasis">数据处理流程:</span>
              <div class="code-block">
                <pre><code># 核心数据处理流水线
1. 数据爬取 → 多源信息收集
2. 数据清洗 → 去重、格式标准化
3. 信息提取 → NLP 提取关键信息
4. 质量评估 → 项目可信度评分
5. 数据存储 → 结构化存储到数据库</code></pre>
              </div>
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">关键技术实现</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">反爬虫对抗策略:</span> 维护
              100+
              高质量代理，自动检测和轮换；模拟真实浏览器行为，随机化请求间隔；集成
              OCR 识别处理简单图形验证码。
            </li>
            <li>
              <span class="text-strong-emphasis">数据质量保证:</span>
              同一项目信息从 3+
              个源进行交叉验证；基于统计学方法识别异常数据点；基于时间戳和内容哈希的智能增量爬取。
            </li>
            <li>
              <span class="text-strong-emphasis">性能优化:</span>
              合理设置并发数；Redis 缓存热点数据；支持爬取任务中断后的断点续传。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            项目成果与价值
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">数据规模:</span> 成功爬取 5000+
              Web3 项目信息，包含 20+ 维度；日更新 500+ 项目信息，数据新鲜度
              95%+。
            </li>
            <li>
              <span class="text-strong-emphasis">技术指标:</span> 单机日处理 10
              万+ 页面，成功率 98%+；数据准确率 95%+；7×24 小时稳定运行，故障率
              &lt;1%。
            </li>
            <li>
              <span class="text-strong-emphasis">商业价值:</span>
              为投资分析提供全面的项目信息基础；通过数据分析识别潜在风险项目，准确率
              85%+；发现行业趋势和热点方向，辅助投资策略制定。
            </li>
          </ul>
        </div>
      </section>

      <!-- Business Projects & Tech Consulting Section -->
      <section class="mb-12">
        <h2 class="section-heading">商业项目与技术咨询</h2>

        <!-- Independent Tech Solutions Provider -->
        <div class="mb-10 pb-8">
          <h3 class="text-2xl font-bold text-gray-800 mb-3">
            独立技术解决方案提供商
            <span class="text-base font-normal text-gray-500 ml-2">| 2024</span>
          </h3>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">业务概述</h4>
          <p class="text-gray-700 space-y-1.5">
            为多家企业和个人客户提供定制化技术解决方案，涵盖 AI
            自动化、区块链支付、Web 应用开发等领域。
          </p>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">商业成果</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">客户服务:</span> 累计服务客户
              15+ 家，项目成功交付率 100%。
            </li>
            <li>
              <span class="text-strong-emphasis">营收成果:</span>
              技术服务累计创收
              <strong class="text-strong-emphasis">10 万+</strong>。
            </li>
            <li>
              <span class="text-strong-emphasis">客户满意度:</span>
              98%，与多个客户建立长期合作关系。
            </li>
            <li>
              <span class="text-strong-emphasis">技术影响:</span>
              帮助客户实现业务效率提升 5-10 倍，成本节省 20-100%。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">核心服务能力</h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">AI 自动化解决方案:</span>
              为电商客户开发视频生成自动化系统，显著节省人工成本；为内容创作者提供
              AI 写作工具，大幅提升创作效率。
            </li>
            <li>
              <span class="text-strong-emphasis">区块链支付系统:</span>
              为金融科技公司开发 USDT 充值系统，有效节省第三方手续费 1%；为 RCS
              平台集成加密货币支付。
            </li>
            <li>
              <span class="text-strong-emphasis">企业 Web 应用:</span>
              为咨询公司开发业务管理平台，显著提升客户管理效率；为传统企业提供数字化转型方案，优化业务流程。
            </li>
          </ul>

          <h4 class="text-xl font-semibold text-gray-800 mb-3">
            商业化思维体现
          </h4>
          <ul class="list-disc pl-5 text-gray-700 space-y-1.5">
            <li>
              <span class="text-strong-emphasis">需求洞察:</span>
              深入理解客户业务痛点，提供针对性技术解决方案。
            </li>
            <li>
              <span class="text-strong-emphasis">成本控制:</span>
              通过技术选型优化，帮助客户实现成本节省和效率提升。
            </li>
            <li>
              <span class="text-strong-emphasis">价值交付:</span>
              不仅交付代码，更交付可量化的业务价值。
            </li>
            <li>
              <span class="text-strong-emphasis">长期合作:</span>
              建立技术顾问关系，为客户提供持续的技术支持。
            </li>
          </ul>
        </div>
      </section>

      <!-- Technical Skills Section -->
      <section class="mb-12">
        <h2 class="section-heading">技术栈</h2>
        <div class="flex flex-wrap gap-2">
          <span class="tag">编程语言: Go</span>
          <span class="tag">JavaScript/TypeScript</span>
          <span class="tag">Python</span>
          <span class="tag">后端框架: Gin</span>
          <span class="tag">GORM</span>
          <span class="tag">前端框架: React 18</span>
          <span class="tag">Vue 3</span>
          <span class="tag">数据库: MySQL</span>
          <span class="tag">Redis</span>
          <span class="tag">SQLite</span>
          <span class="tag">区块链: 以太坊</span>
          <span class="tag">BSC 网络</span>
          <span class="tag">DeepSeek API</span>
          <span class="tag">Luma API</span>
          <span class="tag">Haiper API</span>
          <span class="tag">Docker</span>
          <span class="tag">Git</span>
          <span class="tag">n8n 工作流</span>
          <span class="tag">数据采集: Python 爬虫</span>
          <span class="tag">Scrapy</span>
          <span class="tag">Selenium</span>
          <span class="tag">数据清洗</span>
          <span class="tag">Vite</span>
        </div>
      </section>

     
  </body>
</html>
