<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>杨警名 - 工业设计师</title>
    <style>
      /* 真正的工业设计美学 - <PERSON>er <PERSON> + 瑞士设计 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        --black: #1d1d1f;
        --dark-gray: #424245;
        --medium-gray: #86868b;
        --light-gray: #d2d2d7;
        --very-light-gray: #f5f5f7;
        --white: #ffffff;
        --apple-blue: #007aff;
        --apple-blue-light: #5ac8fa;
        --warm-gray: #f2f2f7;
        --border-light: #e5e5ea;
        --grid-unit: 24px;
        --line-height: 1.4;
      }

      body {
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        background: var(--warm-gray);
        color: var(--black);
        line-height: var(--line-height);
        font-weight: 400;
        font-size: 11pt;
        -webkit-font-smoothing: antialiased;
        margin: 0;
        padding: calc(var(--grid-unit) * 2);
      }

      .resume-container {
        max-width: 210mm;
        min-height: 297mm;
        margin: 0 auto;
        background: var(--white);
        display: grid;
        grid-template-columns: 70mm 1fr;
        grid-gap: 0;
        border: 1px solid var(--border-light);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      }

      /* 左侧信息栏 - 苹果风格 */
      .sidebar {
        background: var(--very-light-gray);
        padding: var(--grid-unit);
        border-right: 1px solid var(--border-light);
      }
 
      .info-section {
        margin-bottom: var(--grid-unit);
        padding-bottom: calc(var(--grid-unit) / 2);
      }

      .info-section:not(:last-child) {
        border-bottom: 1px solid var(--border-light);
      }

      .section-label {
        font-size: 8pt;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        color: var(--medium-gray);
        margin-bottom: calc(var(--grid-unit) / 3);
      }

      .name {
        font-size: 18pt;
        font-weight: 300;
        margin-bottom: calc(var(--grid-unit) / 4);
        color: var(--black);
      }

      .title {
        font-size: 10pt;
        color: var(--dark-gray);
        font-weight: 400;
      }

      .contact-item {
        font-size: 9pt;
        color: var(--dark-gray);
        margin-bottom: calc(var(--grid-unit) / 4);
        line-height: 1.3;
      }

      .skill-item {
        font-size: 9pt;
        color: var(--dark-gray);
        margin-bottom: calc(var(--grid-unit) / 6);
        line-height: 1.2;
      }

      .skill-level {
        font-size: 8pt;
        color: var(--medium-gray);
        font-weight: 300;
      }

      /* 主要内容区域 - 严格网格 */
      .main-content {
        padding: var(--grid-unit);
        background: var(--white);
      }

      .content-section {
        margin-bottom: calc(var(--grid-unit) * 1.5);
      }

      .section-title {
        font-size: 12pt;
        font-weight: 700;
        margin-bottom: calc(var(--grid-unit) / 2);
        color: var(--black);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border-bottom: 1pt solid var(--apple-blue);
        padding-bottom: calc(var(--grid-unit) / 6);
      }

      .intro-text {
        font-size: 10pt;
        line-height: 1.5;
        color: var(--dark-gray);
        margin-bottom: var(--grid-unit);
      }

      /* 教育背景 */
      .education-item {
        margin-bottom: calc(var(--grid-unit) / 2);
      }

      .education-header {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        margin-bottom: calc(var(--grid-unit) / 6);
      }

      .education-school {
        font-size: 11pt;
        font-weight: 500;
        color: var(--black);
      }

      .education-year {
        font-size: 9pt;
        color: var(--medium-gray);
        font-weight: 300;
      }

      .education-degree {
        font-size: 10pt;
        color: var(--dark-gray);
      }

      /* 项目经历 - 极简设计 */
      .project-item {
        margin-bottom: var(--grid-unit);
        padding-bottom: calc(var(--grid-unit) / 2);
        border-bottom: 0.5pt solid var(--border-light);
      }

      .project-item:last-child {
        border-bottom: none;
      }

      .project-header {
        margin-bottom: calc(var(--grid-unit) / 3);
      }

      .project-title {
        font-size: 11pt;
        font-weight: 500;
        color: var(--black);
        margin-bottom: calc(var(--grid-unit) / 6);
      }

      .project-role {
        font-size: 9pt;
        color: var(--medium-gray);
        font-weight: 300;
      }

      .project-description {
        font-size: 10pt;
        line-height: 1.4;
        color: var(--dark-gray);
        margin-bottom: calc(var(--grid-unit) / 3);
      }

      .project-tags {
        font-size: 8pt;
        color: var(--medium-gray);
        font-weight: 300;
      }

      /* 作品集 - 功能性设计 */
      .portfolio-section {
        text-align: left;
        padding: calc(var(--grid-unit) / 2);
        border: 1pt solid var(--light-gray);
        margin-top: var(--grid-unit);
      }

      .portfolio-link {
        color: var(--apple-blue);
        text-decoration: none;
        font-size: 10pt;
        font-weight: 500;
        border-bottom: 1pt solid var(--apple-blue);
        padding-bottom: 1pt;
        transition: color 0.2s ease;
      }

      .portfolio-link:hover {
        color: var(--apple-blue-light);
        border-bottom-color: var(--apple-blue-light);
      }

      .portfolio-description {
        margin-top: calc(var(--grid-unit) / 3);
        font-size: 9pt;
        color: var(--medium-gray);
        line-height: 1.3;
      }

      /* 响应式 - 保持设计完整性 */
      @media (max-width: 768px) {
        body {
          padding: calc(var(--grid-unit) / 2);
        }

        .resume-container {
          grid-template-columns: 1fr;
          max-width: 100%;
        }

        .sidebar {
          border-right: none;
          border-bottom: 1px solid var(--light-gray);
        }
      }

      @media print {
        body {
          padding: 0;
          /* 打印时隐藏SVG网格背景，避免干扰内容 */
          background: none !important;
        }

        .resume-container {
          border: none;
          box-shadow: none;
        }
      }
    </style>
  </head>
  <body>
    <!-- 引导动画：极简小人递简历，苹果风格，SVG+CSS+JS自包含 -->
    <div id="intro-animation" style="position:fixed;z-index:9999;inset:0;display:flex;align-items:center;justify-content:center;background:var(--warm-gray);transition:opacity 0.8s;">
      <!-- 极简SVG动画：美化后大眼睛撑满屏幕 -->
      <svg id="svg-eyes" width="100vw" height="100vh" viewBox="0 0 1200 600" fill="none" xmlns="http://www.w3.org/2000/svg" style="display:block;width:100vw;height:100vh;">
        <defs>
          <!-- 眼白渐变（灰白自然，不用蓝色） -->
          <radialGradient id="eyeWhiteGrad" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stop-color="#fff"/>
            <stop offset="80%" stop-color="#f5f5f7"/>
            <stop offset="100%" stop-color="#e5e5ea"/>
          </radialGradient>
          <!-- 眼白阴影 -->
          <filter id="eyeShadow" x="-30%" y="-30%" width="160%" height="160%">
            <feDropShadow dx="0" dy="18" stdDeviation="18" flood-color="#86868b" flood-opacity="0.13"/>
          </filter>
          <!-- 瞳孔渐变 -->
          <radialGradient id="pupilGrad" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stop-color="#fff" stop-opacity="0.7"/>
            <stop offset="60%" stop-color="#4a5a6a" stop-opacity="0.7"/>
            <stop offset="100%" stop-color="#222"/>
          </radialGradient>
          <!-- 瞳孔深色边缘 -->
          <radialGradient id="pupilEdge" cx="50%" cy="50%" r="50%">
            <stop offset="80%" stop-color="#222" stop-opacity="0"/>
            <stop offset="100%" stop-color="#222" stop-opacity="0.7"/>
          </radialGradient>
        </defs>
        <!-- 左眼白 -->
        <ellipse cx="350" cy="300" rx="260" ry="260" fill="url(#eyeWhiteGrad)" stroke="#e5e5ea" stroke-width="10" filter="url(#eyeShadow)"/>
        <!-- 右眼白 -->
        <ellipse cx="850" cy="300" rx="260" ry="260" fill="url(#eyeWhiteGrad)" stroke="#e5e5ea" stroke-width="10" filter="url(#eyeShadow)"/>
        <!-- 左眼珠 -->
        <ellipse id="pupil-left" cx="350" cy="300" rx="80" ry="80" fill="url(#pupilGrad)"/>
        <ellipse id="pupil-left-edge" cx="350" cy="300" rx="80" ry="80" fill="url(#pupilEdge)"/>
        <!-- 右眼珠 -->
        <ellipse id="pupil-right" cx="850" cy="300" rx="80" ry="80" fill="url(#pupilGrad)"/>
        <ellipse id="pupil-right-edge" cx="850" cy="300" rx="80" ry="80" fill="url(#pupilEdge)"/>
        <!-- 左高光 -->
        <ellipse id="highlight-left" cx="320" cy="270" rx="32" ry="18" fill="#fff" opacity="0.85"/>
        <ellipse id="highlight-left2" cx="370" cy="320" rx="10" ry="6" fill="#fff" opacity="0.45"/>
        <!-- 右高光 -->
        <ellipse id="highlight-right" cx="820" cy="270" rx="32" ry="18" fill="#fff" opacity="0.85"/>
        <ellipse id="highlight-right2" cx="870" cy="320" rx="10" ry="6" fill="#fff" opacity="0.45"/>
      </svg>
      <!-- 跳过动画按钮 -->
      <button id="skip-intro" style="position:absolute;bottom:40px;right:40px;padding:10px 22px;border-radius:24px;border:none;background:#e5e5ea;color:#424245;font-size:14px;cursor:pointer;box-shadow:0 2px 8px rgba(0,0,0,0.06);">跳过动画</button>
    </div>
    <script>
      window.onload = function() {
        try {
          const intro = document.getElementById('intro-animation');
          const skipBtn = document.getElementById('skip-intro');
          const mainContent = document.querySelector('.resume-container');
          const pupilLeft = document.getElementById('pupil-left');
          const pupilRight = document.getElementById('pupil-right');
          const pupilLeftEdge = document.getElementById('pupil-left-edge');
          const pupilRightEdge = document.getElementById('pupil-right-edge');
          const highlightLeft = document.getElementById('highlight-left');
          const highlightLeft2 = document.getElementById('highlight-left2');
          const highlightRight = document.getElementById('highlight-right');
          const highlightRight2 = document.getElementById('highlight-right2');
          // 初始主内容隐藏+缩小
          mainContent.style.opacity = '0';
          mainContent.style.transform = 'scale(0.97)';
          mainContent.style.transition = 'opacity 0.8s, transform 0.8s';

          // 真实人眼运动范围（椭圆内最大偏移40px）
          function setEyes(offsetLx, offsetLy, offsetRx, offsetRy, hLx, hLy, hL2x, hL2y, hRx, hRy, hR2x, hR2y) {
            pupilLeft.setAttribute('cx', 350 + offsetLx);
            pupilLeft.setAttribute('cy', 300 + offsetLy);
            pupilLeftEdge.setAttribute('cx', 350 + offsetLx);
            pupilLeftEdge.setAttribute('cy', 300 + offsetLy);
            pupilRight.setAttribute('cx', 850 + offsetRx);
            pupilRight.setAttribute('cy', 300 + offsetRy);
            pupilRightEdge.setAttribute('cx', 850 + offsetRx);
            pupilRightEdge.setAttribute('cy', 300 + offsetRy);
            highlightLeft.setAttribute('cx', 320 + hLx);
            highlightLeft.setAttribute('cy', 270 + hLy);
            highlightLeft2.setAttribute('cx', 370 + hL2x);
            highlightLeft2.setAttribute('cy', 320 + hL2y);
            highlightRight.setAttribute('cx', 820 + hRx);
            highlightRight.setAttribute('cy', 270 + hRy);
            highlightRight2.setAttribute('cx', 870 + hR2x);
            highlightRight2.setAttribute('cy', 320 + hR2y);
          }
          // 初始正中
          setEyes(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
          // 右看（椭圆路径）
          setTimeout(() => setEyes(32, 10, 32, 10, 8, 2, 8, 2, 8, 2, 8, 2), 350);
          // 左看
          setTimeout(() => setEyes(-32, 10, -32, 10, -8, 2, -8, 2, -8, 2, -8, 2), 950);
          // 上看
          setTimeout(() => setEyes(0, -28, 0, -28, 0, -8, 0, -8, 0, -8, 0, -8), 1550);
          // 下看
          setTimeout(() => setEyes(0, 28, 0, 28, 0, 8, 0, 8, 0, 8, 0, 8), 2100);
          // 回中
          setTimeout(() => setEyes(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0), 2600);

          // 动画结束后淡出intro，主内容scale+fade展开
          setTimeout(() => {
            intro.style.opacity = '0';
            mainContent.style.opacity = '1';
            mainContent.style.transform = 'scale(1)';
            setTimeout(() => { intro.style.display = 'none'; }, 900);
          }, 3400);
          // 跳过动画
          skipBtn.onclick = () => {
            intro.style.opacity = '0';
            mainContent.style.opacity = '1';
            mainContent.style.transform = 'scale(1)';
            setTimeout(() => { intro.style.display = 'none'; }, 900);
          };
        } catch (e) {
          var mainContent = document.querySelector('.resume-container');
          if(mainContent) {
            mainContent.style.opacity = '1';
            mainContent.style.transform = 'scale(1)';
          }
          var intro = document.getElementById('intro-animation');
          if(intro) {
            intro.style.display = 'none';
          }
        }
      };
    </script>
    <!-- 网格背景SVG，极浅灰色，24px间距，苹果风格，直接嵌入，供CSS引用 -->
    <svg id="bg-grid-svg" width="24" height="24" viewBox="0 0 24 24" style="display:none;">
      <defs>
        <pattern id="gridPattern" width="24" height="24" patternUnits="userSpaceOnUse">
          <rect x="0" y="0" width="24" height="24" fill="none" />
          <path d="M 24 0 L 0 0 0 24" fill="none" stroke="#e5e5ea" stroke-width="1" stroke-opacity="0.5"/>
        </pattern>
      </defs>
    </svg>
    <style>
      /* 网格背景应用于body，极浅灰色，苹果风格，SVG嵌入 */
      body {
        background-color: var(--warm-gray);
        background-image: url('data:image/svg+xml;utf8,<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><g><rect x="0" y="0" width="24" height="24" fill="none"/><path d="M24 0L0 0 0 24" fill="none" stroke="%23e5e5ea" stroke-width="1" stroke-opacity="0.5"/></g></svg>');
        background-repeat: repeat;
        background-size: 24px 24px;
      }
    </style>
    <div class="resume-container">
      <!-- 左侧信息栏 -->
      <aside class="sidebar">
        <div class="info-section">
          <div class="section-label">姓名</div>
          <div class="name">杨警名</div>
          <div class="title">工业设计师</div>
        </div>

        <div class="info-section">
          <div class="section-label">联系方式</div>
          <div class="contact-item"><EMAIL></div>
          <div class="contact-item">18380860914</div>
          <div class="contact-item">成都，四川</div>
        </div>

        <div class="info-section">
          <div class="section-label">核心技能</div>
          <div class="skill-item">
            Unity3D 游戏开发 <span class="skill-level">熟练</span>
          </div>
          <div class="skill-item">
            SolidWorks 建模 <span class="skill-level">熟练</span>
          </div>
          <div class="skill-item">
            AR/VR 交互设计 <span class="skill-level">熟练</span>
          </div>
          <div class="skill-item">
            KeyShot 渲染 <span class="skill-level">熟练</span>
          </div>
          <div class="skill-item">
            C# 编程 <span class="skill-level">良好</span>
          </div>
        </div>

        <div class="info-section">
          <div class="section-label">软件工具</div>
          <div class="skill-item">SolidWorks</div>
          <div class="skill-item">Rhino</div>
          <div class="skill-item">KeyShot</div>
          <div class="skill-item">V-Ray</div>
          <div class="skill-item">Fusion 360</div>
          <div class="skill-item">Unity3D</div>
          <div class="skill-item">Arduino</div>
          <div class="skill-item">ESP32</div>
        </div>

        <div class="info-section">
          <div class="section-label">作品集</div>
          <a href="https://risingai.xyz/xrcyblog" class="portfolio-link"
            >risingai.xyz/xrcyblog</a
          >
          <div class="portfolio-description">
            完整设计项目案例<br />
            设计思路分析<br />
            技术实现过程
          </div>
        </div>
      </aside>

      <!-- 主要内容区域 -->
      <main class="main-content">
        <!-- 个人简介 -->
        <section class="content-section">
          <h2 class="section-title">简介</h2>
          <p class="intro-text">
            工业设计师，专注游戏设计、AR/VR交互体验和智能产品设计。具备Unity3D游戏开发和硬件交互编程能力，能够将创意设计与技术实现结合，创造实用的产品体验。
          </p>
        </section>

        <!-- 教育背景 -->
        <section class="content-section">
          <h2 class="section-title">教育背景</h2>
          <div class="education-item">
            <div class="education-header">
              <div class="education-school">西南科技大学</div>
              <div class="education-year">2022 - 2026</div>
            </div>
            <div class="education-degree">工业设计专业 · 本科</div>
          </div>
        </section>

        <!-- 项目经历 -->
        <section class="content-section">
          <h2 class="section-title">项目经历</h2>

          <div class="project-item">
            <div class="project-header">
              <div class="project-title">2D游戏《王国之梦》设计与开发</div>
              <div class="project-role">主设计师 & 核心开发者</div>
            </div>
            <p class="project-description">
              利用Unity引擎开发完整2D游戏。负责游戏世界观构建、关卡设计、角色与场景美术风格定义，进行UI/UX设计。在Unity中完成场景搭建、角色动画整合、音效集成，实现核心游戏逻辑和交互系统。
            </p>
            <div class="project-tags">
              Unity3D · 游戏设计 · UI/UX设计 · C#编程 · 关卡设计
            </div>
          </div>

          <div class="project-item">
            <div class="project-header">
              <div class="project-title">AR交互体验原型设计与开发</div>
              <div class="project-role">核心设计师 & Unity开发者</div>
            </div>
            <p class="project-description">
              基于Unity平台开发AR应用原型。定义AR场景中虚拟物体与真实环境的交互逻辑，设计用户界面和体验，确保虚实融合自然感。利用Unity引擎进行3D模型导入、材质渲染，实现空间识别与追踪功能。
            </p>
            <div class="project-tags">
              AR开发 · Unity3D · 空间交互 · 3D建模 · 用户体验
            </div>
          </div>

          <div class="project-item">
            <div class="project-header">
              <div class="project-title">AI视频生成平台界面设计</div>
              <div class="project-role">UI/UX设计师</div>
            </div>
            <p class="project-description">
              为AI视频生成自动化平台设计用户界面，集成多个AI服务。负责整体用户体验设计，实现中文文字→AI润色→视频生成的全自动化流水线界面，注重操作流程直观性和易用性。
            </p>
            <div class="project-tags">
              界面设计 · 用户体验 · 流程设计 · AI产品
            </div>
          </div>
        </section>
      </main>
    </div>
  </body>
</html>
