<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> - Senior Full Stack Engineer</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1a202c;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 40px 20px;
        }

        .resume-container {
            max-width: 210mm;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            overflow: hidden;
            position: relative;
        }

        .resume-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #4f46e5, #06b6d4, #10b981, #f59e0b, #ef4444);
        }

        .container-inner {
            padding: 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px;
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(6, 182, 212, 0.1) 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
            50% { transform: translate(-50%, -50%) rotate(180deg); }
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 50%, #10b981 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .header .title {
            font-size: 1.5rem;
            color: #475569;
            font-weight: 500;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 18px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .contact-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.25);
        }

        .contact-item span:first-child {
            font-size: 1.2rem;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 25px;
            padding: 20px 25px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 16px;
            border-left: 6px solid #4f46e5;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            color: #1e293b;
        }

        .section h2 span {
            font-size: 1.5rem;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }

        .section h3 {
            font-size: 1.3rem;
            color: #1e293b;
            margin: 25px 0 15px 0;
            font-weight: 600;
        }

        .section h4 {
            font-size: 1.1rem;
            color: #475569;
            margin: 20px 0 12px 0;
            font-weight: 600;
        }

        .tech-stack {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            padding: 25px;
            border-radius: 16px;
            border-left: 6px solid #06b6d4;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .tech-stack::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(6, 182, 212, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .tech-stack h3 {
            color: #0e7490;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .tech-stack ul {
            list-style: none;
            position: relative;
            z-index: 1;
        }

        .tech-stack li {
            margin-bottom: 12px;
            padding-left: 25px;
            position: relative;
            color: #0f172a;
        }

        .tech-stack li:before {
            content: "✦";
            color: #06b6d4;
            position: absolute;
            left: 0;
            font-size: 1.2rem;
        }

        .project {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .project::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4f46e5, #06b6d4, #10b981);
        }

        .project:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .project-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .project-date {
            color: #64748b;
            font-size: 0.9rem;
            background: rgba(100, 116, 139, 0.1);
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 500;
        }

        .tech-tags {
            background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            margin: 15px 0;
            display: inline-block;
            font-weight: 500;
            box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.3);
        }

        .highlight {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            padding: 4px 10px;
            border-radius: 8px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(146, 64, 14, 0.1);
        }

        .metrics {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border-left: 6px solid #10b981;
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .metrics h4 {
            color: #047857;
            margin-bottom: 12px;
        }

        .code-snippet {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            margin: 20px 0;
            overflow-x: auto;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            color: #475569;
        }

        ul, ol {
            margin-left: 25px;
            margin-bottom: 15px;
        }

        li {
            margin-bottom: 8px;
            color: #374151;
        }

        strong {
            color: #1e293b;
            font-weight: 600;
        }

        .revenue-highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 16px;
            margin: 25px 0;
            box-shadow: 0 10px 15px -3px rgba(102, 126, 234, 0.4);
            position: relative;
            overflow: hidden;
        }

        .revenue-highlight::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 8s ease-in-out infinite reverse;
        }

        .revenue-highlight h4 {
            color: white;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .revenue-highlight ul {
            position: relative;
            z-index: 1;
        }

        .revenue-highlight li {
            color: rgba(255, 255, 255, 0.95);
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .skill-category {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
            padding: 25px;
            border-radius: 16px;
            border-left: 6px solid #4f46e5;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .skill-category:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .skill-category h4 {
            margin-top: 0;
            color: #4f46e5;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .skill-category p {
            color: #475569;
            line-height: 1.6;
        }

        @media print {
            body {
                background: none !important;
                padding: 0 !important;
            }
            
            .resume-container {
                max-width: none !important;
                margin: 0 !important;
                background: white !important;
                backdrop-filter: none !important;
                border-radius: 0 !important;
                box-shadow: none !important;
            }
            
            .resume-container::before {
                display: none !important;
            }
            
            .container-inner {
                padding: 15mm !important;
            }
            
            .header {
                background: none !important;
                border-radius: 0 !important;
                padding: 20px !important;
                margin-bottom: 30px !important;
            }
            
            .header::before {
                display: none !important;
            }
            
            .header h1 {
                font-size: 28pt !important;
                background: none !important;
                -webkit-text-fill-color: #1e293b !important;
                color: #1e293b !important;
            }
            
            .section h2 {
                font-size: 16pt !important;
                background: none !important;
                border-left: 4px solid #4f46e5 !important;
                padding: 10px 15px !important;
                box-shadow: none !important;
            }
            
            .project {
                page-break-inside: avoid;
                box-shadow: none !important;
                border: 1px solid #e2e8f0 !important;
                background: white !important;
                margin-bottom: 15px !important;
                padding: 20px !important;
            }
            
            .project::before {
                display: none !important;
            }
            
            .contact-info {
                font-size: 10pt !important;
                display: flex !important;
                flex-wrap: wrap !important;
                gap: 10px !important;
            }
            
            .contact-item {
                background: none !important;
                box-shadow: none !important;
                border: 1px solid #e2e8f0 !important;
                padding: 8px 12px !important;
            }
            
            .tech-stack, .metrics, .revenue-highlight {
                background: #f8fafc !important;
                box-shadow: none !important;
            }
            
            .tech-stack::before, .revenue-highlight::before {
                display: none !important;
            }
            
            .revenue-highlight {
                background: #f1f5f9 !important;
                color: #1e293b !important;
                border: 2px solid #4f46e5 !important;
            }
            
            .revenue-highlight h4, .revenue-highlight li {
                color: #1e293b !important;
            }
            
            .skill-category {
                background: white !important;
                box-shadow: none !important;
                border: 1px solid #e2e8f0 !important;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <div class="container-inner">
            <header class="header">
        <h1>Cyrus Yang</h1>
        <p class="title">Senior Full Stack Engineer / Technical Architect</p>
        <div class="contact-info">
            <div class="contact-item">
                <span>📧</span>
                <span><strong>Email:</strong> <EMAIL></span>
            </div>
            <div class="contact-item">
                <span>📱</span>
                <span><strong>Phone:</strong> +86 18380860914</span>
            </div>
            <div class="contact-item">
                <span>🌐</span>
                <span><strong>Location:</strong> Remote</span>
            </div>
            <div class="contact-item">
                <span>💼</span>
                <span><strong>LinkedIn:</strong> LinkedIn Profile</span>
            </div>
            <div class="contact-item">
                <span>🔗</span>
                <span><strong>GitHub:</strong> GitHub Profile</span>
            </div>
            <div class="contact-item">
                <span>✍️</span>
                <span><strong>Tech Blog:</strong> https://blogxrcy.vercel.app/</span>
            </div>
        </div>
    </header>

    <section class="section">
        <h2><span>👨‍💼</span> Professional Summary</h2>
        <p>Full Stack Engineer with experience in Go and JavaScript/TypeScript development. Experienced in AI service integration, blockchain payment systems, and high-concurrency system development. <span class="highlight">Achieved 100K+ revenue through technical project commercialization, with complete delivery capability from technical development to business implementation.</span></p>
        
        <div class="tech-stack">
            <h3>Tech Stack:</h3>
            <ul>
                <li><strong>Backend:</strong> Go + Gin + GORM, microservices and concurrent programming</li>
                <li><strong>Frontend:</strong> React 18 + TypeScript, Vue 3</li>
                <li><strong>Database:</strong> MySQL, Redis, SQLite</li>
                <li><strong>Blockchain:</strong> Ethereum, BSC network, self-developed USDT deposit system</li>
                <li><strong>AI Integration:</strong> DeepSeek, Luma, Haiper API integration</li>
                <li><strong>Data Collection:</strong> Python crawlers, Scrapy, Selenium, data cleaning</li>
                <li><strong>Tools:</strong> Docker, Git, n8n workflows</li>
            </ul>
        </div>
    </section>

    <section class="section">
        <h2><span>💼</span> Professional Experience</h2>

        <div class="project">
            <div class="project-header">
                <h3 class="project-title">🎬 AI Video Generation Automation Platform</h3>
                <span class="project-date">2024.06</span>
            </div>
            <div class="tech-tags">Tech Stack: n8n + JavaScript + RESTful API</div>
            
            <h4>🤔 Problem Analysis</h4>
            <p>Client needed batch video generation for marketing, facing efficiency bottlenecks:</p>
            <ul>
                <li><strong>Pain Point 1:</strong> Manual processing took 15-20 minutes per video, 100 videos required 2-3 days</li>
                <li><strong>Pain Point 2:</strong> Zapier automation cost $29/month with unstable API calls</li>
            </ul>

            <h4>💡 Solution Strategy</h4>
            <p><strong>Technology Selection Analysis:</strong></p>
            <ul>
                <li>Compared Zapier vs n8n vs pure code solution, found n8n both free and flexible</li>
                <li>Analyzed AI service stability: Luma high quality but prone to timeout, Haiper fast but average quality</li>
            </ul>
            
            <p><strong>Architecture Design Decision:</strong></p>
            <ul>
                <li>Adopted "multi-provider + intelligent degradation" strategy to solve single point of failure</li>
                <li>Designed pipeline: Raw Text → DeepSeek Polishing → Luma/Haiper Video Generation</li>
            </ul>

            <h4>🛠️ Key Technical Implementation</h4>
            <p><strong>Fault Tolerance Design:</strong></p>
            <ul>
                <li>Auto-switch to Haiper when Luma API response exceeds 30 seconds</li>
                <li>Implemented exponential backoff retry: 1s → 2s → 4s, max 3 retries</li>
            </ul>

            <div class="metrics">
                <h4>📊 Solution Validation</h4>
                <ul>
                    <li><strong>Efficiency Improvement:</strong> Single video processing time reduced from 15 minutes to 2 minutes, 87.5% improvement</li>
                    <li><strong>Cost Control:</strong> Monthly operational cost reduced from $29 to $0, 100% savings</li>
                    <li><strong>Stability:</strong> System availability improved from 85% to 99%+ through multi-provider strategy</li>
                </ul>
            </div>
        </div>

        <div class="project">
            <div class="project-header">
                <h3 class="project-title">High-Performance RCS Message Processing System & Blockchain USDT Deposit System</h3>
                <span class="project-date">2024</span>
            </div>
            <div class="tech-tags">Tech Stack: Go 1.21+, Gin, GORM, MySQL, Redis, Ethereum client, Prometheus</div>
            
            <p><strong>Project Description:</strong> Enterprise-level message processing and blockchain payment system supporting high-concurrency message processing and real-time blockchain transaction monitoring.</p>
            
            <h4>Key Contributions:</h4>
            <ul>
                <li><strong>RCS Message System:</strong> Designed worker pool pattern supporting 10-20 concurrent workers, processing <span class="highlight">1000 messages per batch</span>, throughput <span class="highlight">100+ messages/second</span></li>
                <li><strong>Multi-Provider Integration:</strong> Supported multiple RCS providers like Twilio, Infobip, implementing intelligent routing and automatic failover</li>
                <li><strong>Blockchain Integration:</strong> <span class="highlight">Hand-crafted USDT deposit functionality from scratch</span> without relying on any open-source projects, integrated BSC network for real-time USDT transaction monitoring, Goroutine asynchronous deposit confirmation processing, response time <span class="highlight">&lt;10 seconds</span></li>
                <li><strong>Security Protection:</strong> Implemented duplicate deposit prevention, amount validation, state machine management ensuring transaction security</li>
            </ul>

            <div class="metrics">
                <h4>Technical Highlights:</h4>
                <ul>
                    <li><strong>High-Concurrency Architecture:</strong> Goroutine+Channel lock-free concurrency supporting <span class="highlight">1000+ concurrent users</span></li>
                    <li><strong>High-Performance Optimization:</strong> Multi-layer caching architecture with <span class="highlight">95%+ cache hit rate</span>, API response <span class="highlight">&lt;200ms</span></li>
                    <li><strong>Distributed Transactions:</strong> Ensuring data consistency, supporting unlimited-level agent hierarchy and dynamic pricing</li>
                </ul>
            </div>
        </div>

        <div class="project">
            <div class="project-header">
                <h3 class="project-title">AI Intelligent Novel Editor</h3>
                <span class="project-date">2024</span>
            </div>
            <div class="tech-tags">Tech Stack: React 18 + TypeScript + Vite + DeepSeek API</div>
            
            <p>Mimics Cursor IDE's Tab key prediction functionality for novel writing assistance.</p>
            
            <h4>Key Features:</h4>
            <ul>
                <li>Integrated DeepSeek-V3/R1 models</li>
                <li>Tab key to accept predicted text</li>
                <li>Intelligent genre recognition (6 literary genres)</li>
                <li>Context analysis (500 character truncation)</li>
            </ul>

            <h4>Technical Implementation:</h4>
            <ul>
                <li>Gray transparent overlay for prediction text display</li>
                <li>2-second debounce to reduce API calls</li>
                <li>LRU caching strategy, 85% local cache hit rate</li>
                <li>Custom useAutoResize Hook</li>
                <li>TypeScript strict mode, 100% type coverage</li>
            </ul>

            <h4>Innovative Memory Mechanism:</h4>
            <ul>
                <li><strong>Problem:</strong> AI models have context length limitations, causing "forgetting" of previous content in long documents</li>
                <li><strong>Solution:</strong> Designed intelligent memory system that periodically summarizes previous content</li>
                <li><strong>Implementation:</strong>
                    <ul>
                        <li>Triggers memory generation every 2000 characters</li>
                        <li>Uses DeepSeek to summarize key information (characters, plot, style)</li>
                        <li>Incorporates memory as context prefix to maintain writing coherence</li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="project">
            <div class="project-header">
                <h3 class="project-title">Entropy Network - Payment Business Consulting Platform</h3>
                <span class="project-date">2024.12</span>
            </div>
            <div class="tech-tags">Tech Stack: Go + Gin + GORM + SQLite + HTML/CSS/JavaScript + Viper + Logrus</div>
            
            <p><strong>Project Description:</strong> Enterprise-level Web application focused on payment industry vertical domain qualification agency service platform.</p>
            
            <h4>Key Contributions:</h4>
            <ul>
                <li>Adopted layered architecture (Handler-Service-Model-Database), implemented approximately <span class="highlight">5000 lines of high-quality Go code</span></li>
                <li>Designed complete data models and business processes including user registration, service display, requirement management, news and information core functions</li>
                <li>Implemented database migration system supporting versioned database structure management</li>
                <li>Integrated multiple security measures: CSRF protection, XSS protection, SQL injection protection ensuring system security</li>
            </ul>

            <div class="metrics">
                <h4>Technical Highlights:</h4>
                <ul>
                    <li><strong>Performance Optimization:</strong> Average page response time <span class="highlight">&lt;100ms</span>, supporting <span class="highlight">1000+ concurrent user</span> access</li>
                    <li><strong>Engineering Practices:</strong> Modular design, dependency injection, configuration management, unified error handling</li>
                    <li><strong>Operations Support:</strong> Health check interfaces, comprehensive logging, graceful shutdown mechanisms</li>
                </ul>
            </div>
        </div>

        <div class="project">
            <div class="project-header">
                <h3 class="project-title">Freqtrade Quantitative Trading Platform Secondary Development</h3>
                <span class="project-date">2024</span>
            </div>
            <div class="tech-tags">Tech Stack: Python + Freqtrade + Plotly + Pandas + SQLite</div>
            
            <p>Secondary development based on open-source quantitative trading framework Freqtrade, enhancing backtesting mechanisms and data visualization features.</p>
            
            <h4>Key Features:</h4>
            <ul>
                <li>Enhanced backtesting engine supporting multi-strategy parallel backtesting</li>
                <li>Developed data charting module for visualizing trading signals and profit curves</li>
                <li>Optimized strategy parameter tuning workflow</li>
                <li>Integrated risk management and capital management modules</li>
            </ul>

            <h4>Technical Implementation:</h4>
            <ul>
                <li>Extended Freqtrade core backtesting logic</li>
                <li>Built interactive chart system using Plotly</li>
                <li>Pandas data processing and performance analysis</li>
                <li>SQLite storage for backtest results and historical data</li>
                <li>Custom strategy templates and indicator library</li>
            </ul>
        </div>

        <div class="project">
            <div class="project-header">
                <h3 class="project-title">🕷️ Web3 Project Information Crawler System</h3>
                <span class="project-date">2024</span>
            </div>
            <div class="tech-tags">Tech Stack: Python + Scrapy + BeautifulSoup + Selenium + MySQL</div>
            
            <h4>🤔 Business Requirements & Technical Challenges</h4>
            <p><strong>Project Background:</strong></p>
            <ul>
                <li>Need for large-scale data collection and analysis of Web3 project information</li>
                <li>Traditional manual collection is inefficient with outdated data</li>
                <li>Required integration of information from multiple sources (official websites, social media, blockchain explorers)</li>
            </ul>

            <p><strong>Technical Challenges:</strong></p>
            <ul>
                <li><strong>Anti-crawler Countermeasures:</strong> Target websites have complex anti-crawler mechanisms</li>
                <li><strong>Data Quality:</strong> Multi-source data formats are inconsistent, requiring intelligent cleaning and standardization</li>
                <li><strong>Real-time Requirements:</strong> Project information changes rapidly, requiring incremental update mechanisms</li>
            </ul>

            <h4>🛠️ Key Technical Implementation</h4>
            <p><strong>Anti-crawler Countermeasures:</strong></p>
            <ul>
                <li><strong>Proxy Pool Management:</strong> Maintained 100+ high-quality proxies with automatic detection and rotation</li>
                <li><strong>Request Disguise:</strong> Simulated real browser behavior with randomized request intervals</li>
                <li><strong>CAPTCHA Handling:</strong> Integrated OCR recognition for simple graphic CAPTCHAs</li>
            </ul>

            <div class="code-snippet">
# Core data processing pipeline
1. Data Crawling → Multi-source information collection
2. Data Cleaning → Deduplication, format standardization
3. Information Extraction → NLP key information extraction
4. Quality Assessment → Project credibility scoring
5. Data Storage → Structured storage to database
            </div>

            <div class="metrics">
                <h4>📊 Project Results & Value</h4>
                <p><strong>Data Scale:</strong></p>
                <ul>
                    <li><strong>Project Coverage:</strong> Successfully crawled 5000+ Web3 project information</li>
                    <li><strong>Data Dimensions:</strong> 20+ dimensions including basic info, team background, funding, community activity</li>
                    <li><strong>Update Frequency:</strong> Daily updates of 500+ projects, 95%+ data freshness</li>
                </ul>

                <p><strong>Technical Metrics:</strong></p>
                <ul>
                    <li><strong>Crawling Efficiency:</strong> Single machine processes 100K+ pages daily, 98%+ success rate</li>
                    <li><strong>Data Quality:</strong> 95%+ accuracy through multi-source verification</li>
                    <li><strong>System Stability:</strong> 7×24 stable operation, &lt;1% failure rate</li>
                </ul>

                <p><strong>Business Value:</strong></p>
                <ul>
                    <li><strong>Investment Decision Support:</strong> Comprehensive project information foundation for investment analysis</li>
                    <li><strong>Risk Identification:</strong> 85%+ accuracy in identifying potential risk projects through data analysis</li>
                    <li><strong>Market Insights:</strong> Discover industry trends and hotspots to assist investment strategy formulation</li>
                </ul>
            </div>
        </div>
    </section>

    <section class="section">
        <h2><span>💼</span> Commercial Projects & Technical Consulting</h2>
        
        <div class="project">
            <h3>Independent Technical Solution Provider | 2024</h3>
            
            <h4>🎯 Business Overview</h4>
            <p>Providing customized technical solutions for multiple enterprise and individual clients, covering AI automation, blockchain payments, and web application development.</p>

            <div class="revenue-highlight">
                <h4>💰 Commercial Results</h4>
                <ul>
                    <li><strong>Client Services:</strong> Served 15+ clients with 100% project delivery success rate</li>
                    <li><strong>Revenue Achievement:</strong> Technical services generated <strong>100K+ revenue</strong>, average project profit margin 60%+</li>
                    <li><strong>Client Satisfaction:</strong> 98%, established long-term partnerships with multiple clients</li>
                    <li><strong>Technical Impact:</strong> Helped clients achieve 50-300% business efficiency improvement, 20-100% cost savings</li>
                </ul>
            </div>

            <h4>🛠️ Core Service Capabilities</h4>
            <p><strong>AI Automation Solutions:</strong></p>
            <ul>
                <li>Developed video generation automation system for e-commerce clients, saving $2000+ monthly labor costs</li>
                <li>Provided AI writing tools for content creators, improving creation efficiency by 5x</li>
            </ul>

            <p><strong>Blockchain Payment Systems:</strong></p>
            <ul>
                <li>Developed USDT deposit system for fintech companies, saving 1% third-party fees</li>
                <li>Integrated cryptocurrency payments for gaming companies, expanding overseas market revenue by 30%</li>
            </ul>

            <p><strong>Enterprise Web Applications:</strong></p>
            <ul>
                <li>Developed business management platform for consulting companies, improving client management efficiency by 200%</li>
                <li>Provided digital transformation solutions for traditional enterprises, optimizing business processes by 40%</li>
            </ul>

            <h4>🎯 Business Mindset Demonstration</h4>
            <ul>
                <li><strong>Requirement Insight:</strong> Deep understanding of client business pain points, providing targeted technical solutions</li>
                <li><strong>Cost Control:</strong> Through technology selection optimization, helping clients achieve cost savings and efficiency improvements</li>
                <li><strong>Value Delivery:</strong> Not just delivering code, but delivering quantifiable business value</li>
                <li><strong>Long-term Cooperation:</strong> Establishing technical advisory relationships, providing continuous technical support for clients</li>
            </ul>
        </div>
    </section>

    <section class="section">
        <h2><span>🛠️</span> Technical Stack</h2>
        <div class="skills-grid">
            <div class="skill-category">
                <h4>Programming Languages</h4>
                <p>Go, JavaScript/TypeScript, Python</p>
            </div>
            <div class="skill-category">
                <h4>Backend Frameworks</h4>
                <p>Gin, GORM</p>
            </div>
            <div class="skill-category">
                <h4>Frontend Frameworks</h4>
                <p>React 18, Vue 3, TypeScript</p>
            </div>
            <div class="skill-category">
                <h4>Databases</h4>
                <p>MySQL, Redis, SQLite</p>
            </div>
            <div class="skill-category">
                <h4>Blockchain</h4>
                <p>Ethereum, BSC network</p>
            </div>
            <div class="skill-category">
                <h4>Tools</h4>
                <p>Docker, Git, n8n, Vite, Plotly</p>
            </div>
        </div>
    </section>

    <section class="section">
        <h2><span>🎓</span> Education</h2>
        <div class="project">
            <h3>Bachelor's Degree in Computer Science</h3>
            <p><strong>Relevant Coursework:</strong> Data Structures, Algorithms, Database Systems, Network Security</p>
        </div>
    </section>
        </div>
    </div>
</body>
</html> 