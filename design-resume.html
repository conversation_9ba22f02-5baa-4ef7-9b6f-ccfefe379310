<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>杨警名 - 工业设计师简历</title>
    <style>
      /* 苹果风格设计系统 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display",
          "Helvetica Neue", Arial, sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 20px;
        line-height: 1.6;
      }

      .container {
        max-width: 900px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        animation: fadeInUp 0.8s ease-out;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 头部区域 */
      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 40px;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="80" r="1.5" fill="white" opacity="0.1"/><circle cx="40" cy="60" r="1" fill="white" opacity="0.1"/></svg>');
        opacity: 0.3;
      }

      .header-content {
        position: relative;
        z-index: 1;
      }

      .name {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 10px;
        letter-spacing: -0.02em;
      }

      .title {
        font-size: 1.5rem;
        font-weight: 300;
        opacity: 0.9;
        margin-bottom: 30px;
      }

      .contact-info {
        display: flex;
        justify-content: center;
        gap: 30px;
        flex-wrap: wrap;
        font-size: 1rem;
        opacity: 0.8;
      }

      .contact-item {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      /* 主要内容区域 */
      .content {
        padding: 50px 40px;
      }

      .section {
        margin-bottom: 50px;
      }

      .section-title {
        font-size: 1.8rem;
        font-weight: 600;
        color: #1d1d1f;
        margin-bottom: 25px;
        position: relative;
        padding-bottom: 10px;
      }

      .section-title::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
      }

      /* 教育背景 */
      .education-item {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 15px;
        border-left: 4px solid #667eea;
      }

      .education-school {
        font-size: 1.3rem;
        font-weight: 600;
        color: #1d1d1f;
        margin-bottom: 5px;
      }

      .education-degree {
        font-size: 1.1rem;
        color: #667eea;
        margin-bottom: 5px;
      }

      .education-year {
        color: #86868b;
        font-size: 1rem;
      }

      /* 技能标签 */
      .skills-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
      }

      .skill-category {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 15px;
        transition: transform 0.3s ease;
      }

      .skill-category:hover {
        transform: translateY(-5px);
      }

      .skill-category h4 {
        color: #1d1d1f;
        font-size: 1.1rem;
        margin-bottom: 15px;
        font-weight: 600;
      }

      .skill-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      .skill-tag {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
      }

      /* 项目经历 */
      .project-item {
        background: #ffffff;
        border: 1px solid #e5e5e7;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
      }

      .project-item:hover {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .project-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #1d1d1f;
        margin-bottom: 10px;
      }

      .project-description {
        color: #515154;
        margin-bottom: 15px;
        line-height: 1.7;
      }

      .project-tech {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      .tech-tag {
        background: #f0f0f0;
        color: #515154;
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
      }

      /* 作品集链接 */
      .portfolio-link {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 30px;
        border-radius: 15px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        font-size: 1.1rem;
      }

      .portfolio-link:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .container {
          margin: 10px;
          border-radius: 15px;
        }

        .header {
          padding: 40px 20px;
        }

        .name {
          font-size: 2.2rem;
        }

        .title {
          font-size: 1.2rem;
        }

        .contact-info {
          gap: 15px;
          font-size: 0.9rem;
        }

        .content {
          padding: 30px 20px;
        }

        .section-title {
          font-size: 1.5rem;
        }

        .skills-grid {
          grid-template-columns: 1fr;
        }
      }

      @media (max-width: 480px) {
        body {
          padding: 10px;
        }

        .name {
          font-size: 1.8rem;
        }

        .contact-info {
          flex-direction: column;
          gap: 10px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 头部信息 -->
      <header class="header">
        <div class="header-content">
          <h1 class="name">杨警名</h1>
          <p class="title">工业设计师</p>
          <div class="contact-info">
            <div class="contact-item">
              <span>📧</span>
              <span><EMAIL></span>
            </div>
            <div class="contact-item">
              <span>📱</span>
              <span>138-0000-0000</span>
            </div>
            <div class="contact-item">
              <span>🌐</span>
              <span>成都，四川</span>
            </div>
          </div>
        </div>
      </header>

      <!-- 主要内容 -->
      <main class="content">
        <!-- 教育背景 -->
        <section class="section">
          <h2 class="section-title">教育背景</h2>
          <div class="education-item">
            <div class="education-school">西南科技大学</div>
            <div class="education-degree">工业设计专业 · 本科</div>
            <div class="education-year">2022 - 2026</div>
          </div>
        </section>

        <!-- 专业技能 -->
        <section class="section">
          <h2 class="section-title">专业技能</h2>
          <div class="skills-grid">
            <div class="skill-category">
              <h4>工业设计软件</h4>
              <div class="skill-tags">
                <span class="skill-tag">SolidWorks</span>
                <span class="skill-tag">Rhino</span>
                <span class="skill-tag">KeyShot</span>
                <span class="skill-tag">V-Ray</span>
                <span class="skill-tag">Fusion 360</span>
              </div>
            </div>
            <div class="skill-category">
              <h4>游戏与交互设计</h4>
              <div class="skill-tags">
                <span class="skill-tag">Unity3D</span>
                <span class="skill-tag">AR/VR开发</span>
                <span class="skill-tag">游戏UI设计</span>
                <span class="skill-tag">交互原型</span>
                <span class="skill-tag">用户体验</span>
              </div>
            </div>
            <div class="skill-category">
              <h4>辅助技术</h4>
              <div class="skill-tags">
                <span class="skill-tag">C#编程</span>
                <span class="skill-tag">Arduino</span>
                <span class="skill-tag">ESP32</span>
                <span class="skill-tag">AI工具应用</span>
              </div>
            </div>
          </div>
        </section>

        <!-- 项目经历 -->
        <section class="section">
          <h2 class="section-title">项目经历</h2>

          <div class="project-item">
            <h3 class="project-title">2D游戏《王国之梦》设计与开发</h3>
            <p class="project-description">
              担任主设计师与核心开发者，利用Unity引擎开发完成一款完整的2D游戏。负责游戏世界观构建、关卡设计、角色与场景的美术风格定义，并进行UI/UX设计，确保游戏的易玩性和沉浸感。在Unity中进行场景搭建、角色动画整合、音效集成，实现核心游戏逻辑和交互系统。
            </p>
            <div class="project-tech">
              <span class="tech-tag">Unity3D</span>
              <span class="tech-tag">游戏设计</span>
              <span class="tech-tag">UI/UX设计</span>
              <span class="tech-tag">C#编程</span>
              <span class="tech-tag">关卡设计</span>
            </div>
          </div>

          <div class="project-item">
            <h3 class="project-title">AR交互体验原型设计与开发</h3>
            <p class="project-description">
              担任核心设计师与Unity开发者，基于Unity平台开发AR应用原型。负责定义AR场景中虚拟物体与真实环境的交互逻辑，设计直观的用户界面和用户体验，确保虚实融合的自然感。利用Unity引擎进行3D模型导入、材质渲染，实现空间识别与追踪功能，并编写相关脚本优化交互流畅度。
            </p>
            <div class="project-tech">
              <span class="tech-tag">AR开发</span>
              <span class="tech-tag">Unity3D</span>
              <span class="tech-tag">空间交互</span>
              <span class="tech-tag">3D建模</span>
              <span class="tech-tag">用户体验</span>
            </div>
          </div>

          <div class="project-item">
            <h3 class="project-title">AI视频生成自动化平台界面设计</h3>
            <p class="project-description">
              基于n8n开发AI视频生成自动化平台的用户界面设计，集成DeepSeek、Luma、Haiper等多AI服务。负责整体用户体验设计，实现中文文字→AI润色→视频生成的全自动化流水线界面，注重操作流程的直观性和易用性。
            </p>
            <div class="project-tech">
              <span class="tech-tag">界面设计</span>
              <span class="tech-tag">用户体验</span>
              <span class="tech-tag">流程设计</span>
              <span class="tech-tag">AI产品</span>
            </div>
          </div>
        </section>

        <!-- 作品集 -->
        <section class="section">
          <h2 class="section-title">在线作品集</h2>
          <a
            href="https://risingai.xyz/xrcyblog"
            target="_blank"
            class="portfolio-link"
          >
            <span>🎨</span>
            <span>查看我的设计作品集</span>
            <span>→</span>
          </a>
          <p style="margin-top: 15px; color: #86868b; font-size: 0.95rem">
            包含完整的设计项目案例、设计思路分析和技术实现过程
          </p>
        </section>

        <!-- 个人优势 -->
        <section class="section">
          <h2 class="section-title">个人优势</h2>
          <div
            style="
              background: #f8f9fa;
              padding: 25px;
              border-radius: 15px;
              line-height: 1.8;
            "
          >
            <p style="color: #515154; margin-bottom: 15px">
              •
              <strong>游戏与交互设计专长</strong
              >：具备Unity3D游戏开发和AR/VR交互设计经验，能够创造沉浸式用户体验
            </p>
            <p style="color: #515154; margin-bottom: 15px">
              •
              <strong>技术实现能力</strong
              >：掌握C#编程和硬件交互(Arduino/ESP32)，设计方案具备强可行性
            </p>
            <p style="color: #515154; margin-bottom: 15px">
              •
              <strong>全流程设计思维</strong
              >：从概念设计到技术实现的完整项目经验，注重用户体验和产品落地
            </p>
            <p style="color: #515154">
              •
              <strong>前沿技术应用</strong
              >：关注AI辅助设计、智能交互等新兴技术在工业设计中的创新应用
            </p>
          </div>
        </section>
      </main>
    </div>
  </body>
</html>
