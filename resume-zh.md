AI 视频生成自动化平台 | n8n + AI 服务集成 2024.06

• 基于 n8n 开发 AI 视频生成自动化平台，集成 DeepSeek、<PERSON><PERSON>、Haiper 等多 AI 服务
• 实现中文文字 →AI 润色 → 视频生成的全自动化流水线，支持 API 热切换和容错机制  
• 通过技术选型优化从付费方案切换到免费方案，节省 100%运营成本($29/月 → 免费)

# AI 智能小说编辑器 - 核心技术要点总结

## 项目概述

基于 React 18 + TypeScript 开发的智能写作助手，实现类似 Cursor IDE 的 Tab 键预测功能，集成 DeepSeek AI 模型，为用户提供实时文本预测和写作辅助。

## 核心技术亮点

### 1. 前端架构设计

- **现代化技术栈**: React 18 + TypeScript + Vite，确保类型安全和开发效率
- **组件化架构**: 采用函数式组件 + 自定义 Hooks 模式，代码复用率提升 40%
- **响应式设计**: 支持 PC/平板/手机端自适应，兼容性覆盖 95%+主流设备

### 2. AI 集成与优化

- **多模型支持**: 集成 DeepSeek-V3/R1 双模型，根据创作需求智能切换
- **智能体裁识别**: 自研关键词检测算法，自动识别 6 种文学体裁并调整写作风格

教育背景
[您的大学名称] | [您的专业] | [开始年份] - [结束年份]

- 主修课程：产品设计、人机工程学、设计心理学、材料与工艺、CMF 设计、数字建模与渲染等。
- [您的在校荣誉/奖项，例如：优秀学生、奖学金等]
  项目经验

1. AR 交互体验原型设计与开发（Unity 项目）

- 角色： 核心设计师 & Unity 开发者
- 时间： [项目起止时间，例如：2023 年 X 月 - 2024 年 X 月]
- 描述： 独立或团队成员负责基于 Unity 平台开发一款[简要描述 AR 项目类型，例如：AR 家具摆放应用/AR 教育体验/AR 产品展示应用]的原型项目。
  - 设计贡献： 负责定义 AR 场景中的虚拟物体与真实环境的交互逻辑，设计直观的用户界面(UI)和用户体验(UX)，确保虚实融合的自然感。
  - 技术实现： 利用 Unity 引擎进行 3D 模型导入、材质渲染，实现空间识别与追踪功能，并编写相关脚本优化交互流畅度。
  - 成果： 成功构建可交互的 AR 原型，有效验证了特定场景下的设计设想，展现出对空间设计、数字内容整合及前沿交互技术的理解与应用能力。

2. 2D 游戏《王国之梦》设计与开发（Unity 项目）

- 角色： 主设计师 & 核心开发者
- 时间： [项目起止时间，例如：2022 年 X 月 - 2023 年 X 月]
- 描述： 独立或团队成员利用 Unity 引擎开发完成一款名为《王国之梦》的 2D 游戏。
  - 设计贡献： 负责游戏世界观构建、关卡设计、角色与场景的美术风格定义，并进行 UI/UX 设计，确保游戏的易玩性和沉浸感。
  - 技术实现： 在 Unity 中进行场景搭建、角色动画整合、音效集成，并实现核心游戏逻辑和交互系统。
  - 成果： 完成一款具备完整可玩性的 2D 游戏，锻炼了叙事设计、用户反馈循环设计及跨媒体内容整合的能力。

工业设计软件：

- 3D 建模与渲染： SolidWorks, Rhino, Keyshot, V-Ray, Fusion 360
  Arduino (简单硬件交互) esp32

技术开发与引擎： Unity3D (熟练掌握游戏开发、AR/VR 项目开发), C# (基础)
